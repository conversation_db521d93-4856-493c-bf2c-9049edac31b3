import { SelectionModel } from '@angular/cdk/collections';
import { NestedTreeControl } from '@angular/cdk/tree';
import {
  Component,
  computed,
  effect,
  HostListener,
  Input,
  NgZone,
  OnDestroy,
  OnInit,
  signal,
} from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { MatTreeNestedDataSource } from '@angular/material/tree';
import { Router } from '@angular/router';
import { TreeNodeContextMenu } from 'src/app/shared/configs/contextMenuConfig';
import { Diagram } from 'src/app/shared/model/diagram';
import { ConfirmDialogData } from 'src/app/shared/model/dialog';
import { GojsNodeCategory } from 'src/app/shared/model/gojs';
import { AccessType } from 'src/app/shared/model/project';
import {
  ContextMenu,
  ContextMenuAction,
  TreeNode,
} from 'src/app/shared/model/treeNode';
import {
  ClassWrapperCategory,
  DiagramWrapperCategory,
  EnumWrapperCategory,
} from 'src/app/shared/utils/constants';
import { DiagramUtils } from 'src/app/shared/utils/diagram-utils';
import { AccessService } from '../../services/access/access.service';
import { ContextMenuActionService } from '../../services/contextMenuAction/context-menu-action.service';
import { NavbarService } from '../../services/navbar/navbar.service';
import { PropertyService } from '../../services/property/property.service';
import { TreeNodeService } from '../../services/treeNode/tree-node.service';
import { DialogConfirmationComponent } from '../dialog-confirmation/dialog-confirmation.component';
@Component({
  selector: 'app-library-tree',
  templateUrl: './library-tree.component.html',
  styleUrls: ['./library-tree.component.scss'],
})
export class LibraryTreeComponent implements OnInit, OnDestroy {
  // Tree control and data source
  treeControl = new NestedTreeControl<TreeNode>((node) => node.children);
  dataSource = new MatTreeNestedDataSource<TreeNode>();

  // Convert component state to signals
  private _isDisplayContextMenu = signal(false);
  private _rightClickMenuPositionX = signal(0);
  private _rightClickMenuPositionY = signal(0);
  private _currentDiagram = signal<Diagram | null>(null);
  private _currentNode = signal<TreeNode | null>(null);
  private _contextMenuOptions = signal<ContextMenu[]>([]);
  private _hasEditAccessOnly = signal(false);
  private _draggedNode = signal<TreeNode | null>(null);
  private _treeNodeData = signal<TreeNode | null>(null);
  private _selectedTreeNode = signal<TreeNode | null>(null);
  private _selectedNodes = signal<Set<TreeNode>>(new Set());
  private _isRenaming = signal(false);
  private _previousName = signal('');
  private _currentDiagramTag = signal('');
  private _searchResults = signal<TreeNode[]>([]);
  private _hasDiagram = signal(true);
  private _isSearching = signal(false);
  private _showOnlyDiagrams = signal(false);
  private _isDraggedOver = signal(false);
  private _isDragging = signal(false);
  private _currentDragTarget = signal<TreeNode | null>(null);

  // Readonly signals for template access
  readonly isDisplayContextMenu = this._isDisplayContextMenu.asReadonly();
  readonly rightClickMenuPositionX = this._rightClickMenuPositionX.asReadonly();
  readonly rightClickMenuPositionY = this._rightClickMenuPositionY.asReadonly();
  readonly currentDiagram = this._currentDiagram.asReadonly();
  readonly contextMenuOptions = this._contextMenuOptions.asReadonly();
  readonly hasEditAccessOnly = this._hasEditAccessOnly.asReadonly();
  readonly draggedNode = this._draggedNode.asReadonly();
  readonly treeNodeData = this._treeNodeData.asReadonly();
  readonly selectedTreeNode = this._selectedTreeNode.asReadonly();
  readonly selectedNodes = this._selectedNodes.asReadonly();
  readonly isRenaming = this._isRenaming.asReadonly();
  readonly previousName = this._previousName.asReadonly();
  readonly currentDiagramTag = this._currentDiagramTag.asReadonly();
  readonly searchResults = this._searchResults.asReadonly();
  readonly hasDiagram = this._hasDiagram.asReadonly();
  readonly isSearching = this._isSearching.asReadonly();
  readonly showOnlyDiagrams = this._showOnlyDiagrams.asReadonly();
  readonly isDraggedOver = this._isDraggedOver.asReadonly();
  readonly isDragging = this._isDragging.asReadonly();
  readonly currentDragTarget = this._currentDragTarget.asReadonly();

  // Computed signal to check if tree is ready for expansion
  public readonly isTreeReady = computed(() => {
    return this._treeNodeData() !== null && this.dataSource.data.length > 0;
  });

  // Keep selection model for now
  selection = new SelectionModel<TreeNode>(true, []); // true enables multi-select
  // Add host listener for delete key
  @HostListener('window:keyup.delete', ['$event'])
  onDeleteKey() {
    if (this.hasEditAccessOnly() && this.selection.selected.length > 0) {
      this.deleteSelectedNodes();
    }
  }
  @Input() set expandNodeTag(tag: string) {
    if (tag) {
      // Add a delay to ensure the tree is fully rendered
      setTimeout(() => {
        this.expandNodeByTag(tag);
      }, 200);
    }
  }

  constructor(
    private _dialog: MatDialog,
    private treeNodeService: TreeNodeService,
    private diagramUtils: DiagramUtils,
    private _contextMenuActionService: ContextMenuActionService,
    private _accessService: AccessService,
    private _propertyService: PropertyService,
    private _navbarService: NavbarService,
    private _ngZone: NgZone,
    private router: Router
  ) {
    // Effect for navbar version history changes
    effect(() => {
      const isRightPanelOpened = this._navbarService.showVersionHistory();
      this._ngZone.run(() => {
        this.toggleDiagramsView(isRightPanelOpened);
      });
    });

    // Subscribe to library details changes (using existing Observable method)
    this.treeNodeService.getLibraryDetails().subscribe((treeNode) => {
      if (treeNode) {
        this._treeNodeData.set(treeNode);
        this.dataSource.data = [];
        this.dataSource.data = [treeNode];
        const nodes = this.treeControl.getDescendants(treeNode);
        this.treeNodeService.descendantTreeNodes = nodes;

        // If we have a current diagram, expand to show it
        const currentDiagramTag = this._currentDiagramTag();
        if (currentDiagramTag) {
          setTimeout(() => {
            this.expandNodeByTag(currentDiagramTag);
          }, 100);
        }
      }
    });

    // Subscribe to active diagram changes (using existing Observable method)
    this.diagramUtils.activeDiagramChanges().subscribe((diagram) => {
      if (diagram) {
        this._hasDiagram.set(true);
        this._currentDiagram.set(diagram);
        this._currentDiagramTag.set(
          `atTag${GojsNodeCategory.Diagram}_${diagram.id}`
        );

        // When a diagram is activated, expand the tree to show it
        // Add a delay to ensure the tree data is fully loaded
        setTimeout(() => {
          this.expandNodeByTag(this._currentDiagramTag());
        }, 100);
      } else {
        this._propertyService.setPropertyData(null);
        this._hasDiagram.set(false);
      }
    });

    // Subscribe to access type changes (using existing Observable method)
    this._accessService.accessTypeChanges().subscribe((accessType) => {
      this._hasEditAccessOnly.set(accessType != AccessType.Viewer);
    });
  }

  ngOnInit(): void {
    // The subscriptions are now handled in the constructor with effects
    // This method can be used for any additional initialization if needed
  }

  onSearch(searchTerm: string | null) {
    if (searchTerm) {
      this._isSearching.set(true);
      this._searchResults.set(this.searchNodes(searchTerm));
      this._propertyService.setPropertyData(null);
    } else {
      this._isSearching.set(false);
      this._searchResults.set([]);
    }
  }

  toggleDiagramsView(showOnlyDiagrams: boolean): void {
    this._showOnlyDiagrams.set(showOnlyDiagrams);

    // If toggling to diagram-only view, populate the diagrams list
    if (this.showOnlyDiagrams()) {
      this.populateDiagramsList();
    }
  }

  private populateDiagramsList(): void {
    this._searchResults.set(
      this.getAllNodes().filter(
        (node) => node.category === GojsNodeCategory.Diagram
      )
    );
  }
  // Search through nodes recursively
  private searchNodes(
    searchTerm: string,
    nodes: TreeNode[] = this.dataSource.data
  ): TreeNode[] {
    let results: TreeNode[] = [];
    for (const node of nodes) {
      // Check if current node matches search
      if (
        this.nodeFilterCriteria(node) &&
        node.name.toLowerCase().includes(searchTerm.toLowerCase())
      ) {
        results.push(node);
      }
      // If node has children, search them too
      if (node.children && node.children.length > 0) {
        results = results.concat(this.searchNodes(searchTerm, node.children));
      }
    }
    return results;
  }

  private nodeFilterCriteria(node: TreeNode): boolean {
    return (
      node.category !== ClassWrapperCategory &&
      node.category !== EnumWrapperCategory &&
      node.category !== DiagramWrapperCategory &&
      node.category !== GojsNodeCategory.Project
    );
  }

  // Clear search and reset view
  clearSearch() {
    this._searchResults.set([]);
    this._propertyService.setPropertyData(null);
  }

  // Handle selection of search result
  selectSearchResult(event: MouseEvent, node: TreeNode) {
    this.selection.select(node);
    this.selectDiagramNode(event, node);
  }

  // Method to delete selected nodes
  deleteSelectedNodes() {
    const selectedNodes = this.selection.selected;
    if (selectedNodes.length === 0) return;
    const dialogRef = this._dialog.open<
      DialogConfirmationComponent,
      ConfirmDialogData,
      boolean
    >(DialogConfirmationComponent, {
      width: '320px',
      data: {
        title: 'dialog.title',
        reject: 'dialog.no',
        confirm: 'dialog.yes',
      },
    });
    dialogRef.afterClosed().subscribe((isConfirm) => {
      if (isConfirm) {
        // Delete each selected node
        this._contextMenuActionService.deleteSelectedNodes(selectedNodes);
        // Clear selection after deletion
        this.selection.clear();
      }
    });
  }

  hasChild = (_: number, node: TreeNode) => {
    return !!node.children && node.children.length > 0;
  };
  canDiagramDraggable(node: TreeNode): boolean {
    if (node && node.category === GojsNodeCategory.Diagram) {
      const treeNodeData = this.treeNodeData();
      if (treeNodeData) {
        const parentFolderNode = this.treeNodeService.findParentNode(
          node.parentTag!,
          treeNodeData
        );
        if (
          parentFolderNode &&
          parentFolderNode.category === GojsNodeCategory.Folder
        )
          return true;
        else return true;
      }
    }
    return true;
  }
  onRightClick(event: MouseEvent, node: TreeNode): void {
    event.stopPropagation();
    event.preventDefault();
    this._currentNode.set(node);
    const contextMenu = TreeNodeContextMenu.find(
      (menu) => menu.category === node.category && menu.isDisplay
    );

    // If the node is not already selected and we have multiple nodes selected,
    // clear the selection and select only this node
    if (
      this.selection.selected.length > 1 &&
      !this.selection.isSelected(node)
    ) {
      this.selection.clear();
      this.selection.select(node);
    }

    if (contextMenu) {
      this._contextMenuOptions.set(
        this.selection.selected.length <= 1
          ? contextMenu.options!
          : contextMenu.options!.filter((option) => option.label == 'Delete')
      );
      this._isDisplayContextMenu.set(true);
      this._rightClickMenuPositionX.set(event.clientX);
      this._rightClickMenuPositionY.set(event.clientY);
    }
  }

  @HostListener('document:click')
  documentClick(): void {
    this._isDisplayContextMenu.set(false);
    this.selection.clear();
  }

  getRightClickMenuStyle() {
    return {
      position: 'fixed',
      left: `${this.rightClickMenuPositionX()}px`,
      top: `${this.rightClickMenuPositionY()}px`,
    };
  }

  selectDiagramNode(event: MouseEvent, treeNode: TreeNode) {
    event.stopPropagation();
    this._isDisplayContextMenu.set(false);
    if (
      treeNode.category === ClassWrapperCategory ||
      treeNode.category === EnumWrapperCategory ||
      treeNode.category === DiagramWrapperCategory ||
      treeNode.category === GojsNodeCategory.Project ||
      !this.hasDiagram()
    ) {
      this._propertyService.setPropertyData(null);
      return;
    }
    this._selectedTreeNode.set(treeNode);
    if (!this.isDiagram(treeNode.data) && treeNode.data) {
      this._propertyService.setPropertyData(treeNode.data);
      if (event.ctrlKey || event.metaKey) {
        // Toggle selection for ctrl/cmd + click
        this.selection.toggle(treeNode);
      } else if (event.shiftKey && this.selection.selected.length > 0) {
        // Handle shift + click for range selection
        // This is a basic implementation - you might want to enhance it based on your needs
        const lastSelected =
          this.selection.selected[this.selection.selected.length - 1];
        const nodes = this.getAllNodes();
        const startIdx = nodes.indexOf(lastSelected);
        const endIdx = nodes.indexOf(treeNode);
        const range = nodes.slice(
          Math.min(startIdx, endIdx),
          Math.max(startIdx, endIdx) + 1
        );
        range.forEach((n) => {
          if (
            n.category === ClassWrapperCategory ||
            n.category === EnumWrapperCategory ||
            n.category === DiagramWrapperCategory ||
            n.category === GojsNodeCategory.Project ||
            n.category === GojsNodeCategory.Diagram
          ) {
            return;
          }
          this.selection.select(n);
        });
      } else {
        // Single selection
        this.selection.clear();
        this.selection.select(treeNode);
      }
    }
    if (
      treeNode.category === GojsNodeCategory.Diagram &&
      this.currentDiagram()?.id !== treeNode.data?.id
    ) {
      this._propertyService.setPropertyData(null);
      const diagram = treeNode.data as Diagram;
      this.diagramUtils.setActiveDiagram(diagram);

      // Update the URL to include the selected diagram ID
      const projectId = diagram.idProject;
      const diagramId = diagram.id;
      if (projectId && diagramId) {
        this.router.navigate([`/editor/${projectId}/diagram/${diagramId}`], {
          replaceUrl: true, // Replace the current URL instead of adding a new history entry
        });

        // The expandNodeByTag will be called automatically when the diagram is activated
        // through the activeDiagramChanges subscription in ngOnInit
      }
    }
  }

  // Helper method to get all nodes in a flat array
  private getAllNodes(): TreeNode[] {
    const nodes: TreeNode[] = [];
    const getNodesRecursively = (nodesList: TreeNode[]) => {
      nodesList.forEach((node) => {
        nodes.push(node);
        if (node.children) {
          getNodesRecursively(node.children);
        }
      });
    };
    getNodesRecursively(this.dataSource.data);
    return nodes;
  }

  private isDiagram(data: any): data is Diagram {
    return (
      (data as Diagram).id !== undefined &&
      (data as Diagram).idProject !== undefined &&
      data.category === undefined
    );
  }

  onDragStart(event: DragEvent, node: TreeNode) {
    if (event.dataTransfer) {
      event.dataTransfer.setData('text/plain', JSON.stringify(node));
      this._draggedNode.set(node);
      this._isDragging.set(true);

      // Add a class to the dragged element
      const element = event.target as HTMLElement;
      element.classList.add('dragging');
    }
  }

  onAction(action: ContextMenuAction): void {
    const currentNode = this._currentNode();
    if (currentNode) {
      this._contextMenuActionService.executeAction(
        action,
        currentNode,
        this.selection
      );
    }
  }

  onDragOver(event: DragEvent, node: TreeNode) {
    event.preventDefault();
    if (this.isValidDropTarget(node)) {
      event.dataTransfer!.dropEffect = 'move';
    } else {
      event.dataTransfer!.dropEffect = 'none';
    }
  }

  // Handle the node drop
  onMove(event: DragEvent, targetNode: TreeNode) {
    event.preventDefault();
    this._isDraggedOver.set(false);
    this._isDragging.set(false);
    this._currentDragTarget.set(null);

    if (this.isValidDropTarget(targetNode)) {
      const draggedNode = this.draggedNode();
      if (draggedNode) {
        this.treeNodeService.moveNode(targetNode, draggedNode);
      }
    }
  }

  isSelected(node: TreeNode): boolean {
    return this.selectedNodes().has(node);
  }
  @HostListener('window:keyup.f2', [])
  onRenameF2Key() {
    const selectedTreeNode = this.selectedTreeNode();
    if (this.hasEditAccessOnly() && selectedTreeNode && this.hasDiagram()) {
      this.enableRename(selectedTreeNode);
    }
  }
  canRename(node: TreeNode): boolean {
    if (
      node.category == DiagramWrapperCategory ||
      node.category == ClassWrapperCategory ||
      node.category == EnumWrapperCategory ||
      node.category === GojsNodeCategory.Project
    )
      return false;
    else return true;
  }
  enableRename(node: TreeNode) {
    if (!this.hasEditAccessOnly() || !this.hasDiagram()) return;
    if (this.canRename(node)) {
      node.isRenaming = true;
      this._previousName.set(node.name);
    }
    // Set a slight timeout to focus on the input after it appears
    setTimeout(() => {
      const inputElement = document.querySelector(
        '.rename-input'
      ) as HTMLInputElement;
      if (inputElement && this.selectedTreeNode()) {
        inputElement.focus();
        inputElement.select(); // Select text to allow easy overwrite
      }
    });
  }

  saveRename(node: TreeNode, newName: string) {
    const trimmedName = newName.trim();
    const previousName = this.previousName();

    if (trimmedName === '' || trimmedName === previousName) {
      // If input is empty or only whitespace, restore the previous name
      node.name = previousName || node.name;
      const inputElement = document.querySelector(
        '.rename-input'
      ) as HTMLInputElement;
      if (inputElement) {
        inputElement.blur();
      }
    } else {
      // Update with new name if valid
      node.name = trimmedName;
      this._contextMenuActionService.renameNode(node);
    }

    node.isRenaming = false;
    this._previousName.set('');
  }

  cancelRename(node: TreeNode) {
    node.isRenaming = false;
  }
  shouldShowTooltip(name: string, length: number): boolean {
    return name.length > length;
  }

  onDragEnter(event: DragEvent, node: TreeNode) {
    event.preventDefault();
    this._currentDragTarget.set(node);
    this._isDraggedOver.set(true);
  }

  onDragLeave(event: DragEvent, node: TreeNode) {
    event.preventDefault();
    if (this.currentDragTarget() === node) {
      this._isDraggedOver.set(false);
      this._currentDragTarget.set(null);
    }
  }

  isValidDropTarget(node: TreeNode): boolean {
    const draggedNode = this.draggedNode();
    return !!(
      draggedNode &&
      node.supportingNodes?.includes(draggedNode.category) &&
      !node.category.includes('Wrapper') &&
      this.hasDiagram() &&
      this.canDiagramDraggable(draggedNode)
    );
  }

  ngOnDestroy() {
    this._isDraggedOver.set(false);
    this._isDragging.set(false);
    this._currentDragTarget.set(null);
  }
  /**
   * Expands the tree to show a node with the given tag
   * Also expands all parent nodes to make the node visible
   * @param tag The tag of the node to expand to
   */
  expandNodeByTag(tag: string) {
    // Ensure we have tree data before trying to expand
    if (!this.dataSource.data || this.dataSource.data.length === 0) {
      // Retry expansion after a delay if tree data is not ready
      setTimeout(() => this.expandNodeByTag(tag), 200);
      return;
    }

    const node = this.treeNodeService.findNodeByTag(tag);
    if (node) {
      // First expand all parent nodes to make this node visible
      this.expandParentNodes(node);

      // If the node itself has children, expand it too
      if (this.hasChild(0, node) && !this.treeControl.isExpanded(node)) {
        this.treeControl.expand(node);
      }

      // Scroll to the node to make it visible in the viewport
      setTimeout(() => {
        const nodeElement = document.querySelector(`[data-node-tag="${tag}"]`);
        if (nodeElement) {
          nodeElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
        }
      }, 200);
    }
  }

  /**
   * Expands all parent nodes of the given node
   * @param node The node whose parents should be expanded
   */
  private expandParentNodes(node: TreeNode) {
    // Get the parent chain from root to the node
    const parentChain = this.getParentChain(node);

    // Expand each parent in the chain
    parentChain.forEach((parent) => {
      if (!this.treeControl.isExpanded(parent)) {
        this.treeControl.expand(parent);
      }
    });
  }

  /**
   * Gets the chain of parent nodes from root to the given node
   * @param node The node to find parents for
   * @returns Array of parent nodes in order from root to immediate parent
   */
  private getParentChain(node: TreeNode): TreeNode[] {
    const parentChain: TreeNode[] = [];
    let currentNode = node;

    // Traverse up the tree until we reach the root
    while (currentNode.parentTag) {
      const parentNode = this.treeNodeService.findNodeByTag(
        currentNode.parentTag
      );
      if (parentNode) {
        parentChain.unshift(parentNode); // Add to beginning of array
        currentNode = parentNode;
      } else {
        break;
      }
    }

    return parentChain;
  }
}
