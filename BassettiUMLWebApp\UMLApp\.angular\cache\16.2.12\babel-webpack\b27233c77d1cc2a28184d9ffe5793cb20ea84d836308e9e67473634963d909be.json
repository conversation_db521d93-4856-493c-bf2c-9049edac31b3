{"ast": null, "code": "import { computed, signal } from '@angular/core';\nimport { BehaviorSubject } from 'rxjs';\nimport { AttributeMemberType } from 'src/app/shared/model/attribute';\nimport { GoJsNodeIcon } from 'src/app/shared/model/common';\nimport { GojsNodeCategory } from 'src/app/shared/model/gojs';\nimport { TreeNodeTag } from 'src/app/shared/model/treeNode';\nimport { ClassWrapperCategory, DiagramWrapperCategory, DiagramWrapperName, EnumWrapperCategory } from 'src/app/shared/utils/constants';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../data-format/data-format.service\";\nimport * as i2 from \"../class/class.service\";\nimport * as i3 from \"../enumeration/enumeration.service\";\nimport * as i4 from \"../folder/folder.service\";\nimport * as i5 from \"src/app/shared/utils/diagram-utils\";\nexport class TreeNodeService {\n  constructor(dataFormatService, _classService, _enumerationService, _folderService, _diagramUtils) {\n    this.dataFormatService = dataFormatService;\n    this._classService = _classService;\n    this._enumerationService = _enumerationService;\n    this._folderService = _folderService;\n    this._diagramUtils = _diagramUtils;\n    // Convert to signals\n    this._libraryDetails = signal(null);\n    this.libraryDetails = this._libraryDetails.asReadonly();\n    // Keep BehaviorSubject for backward compatibility during transition\n    this.libraryDetailsSubject = new BehaviorSubject(null);\n    this._descendantTreeNodes = [];\n    this.currentDiagramId = -1;\n    // Computed signals for derived data\n    this.descendantNodes = computed(() => {\n      const root = this._libraryDetails();\n      if (!root) return [];\n      const nodes = [];\n      const collectNodes = nodesList => {\n        nodesList.forEach(node => {\n          nodes.push(node);\n          if (node.children) {\n            collectNodes(node.children);\n          }\n        });\n      };\n      collectNodes([root]);\n      return nodes;\n    });\n    this.CATEGORY_PRIORITY = [DiagramWrapperCategory, ClassWrapperCategory, EnumWrapperCategory, GojsNodeCategory.Folder // Always at the end\n    ];\n    this._diagramUtils.activeDiagramChanges().subscribe(diagram => {\n      if (diagram && diagram.id) this.currentDiagramId = diagram.id;\n    });\n  }\n  getLibraryDetails() {\n    return this.libraryDetails.asObservable();\n  }\n  setLibraryDetails(projectDetails) {\n    const formattedNode = this.formatTreeData(projectDetails);\n    return this.libraryDetails.next(formattedNode);\n  }\n  get descendantTreeNodes() {\n    return this._descendantTreeNodes;\n  }\n  set descendantTreeNodes(nodes) {\n    this._descendantTreeNodes = nodes;\n  }\n  getWrapperParentTag(wrapperTag) {\n    return `${wrapperTag}_${TreeNodeTag.Project}`;\n  }\n  formatTreeData(projectDetails) {\n    const treeNode = {\n      name: projectDetails.name,\n      category: GojsNodeCategory.Project,\n      children: [...(projectDetails.diagrams.length > 0 ? [this.formatDiagramTreeNode(projectDetails.diagrams, TreeNodeTag.Project, projectDetails.id)] : []), ...this.formatClassAndEnum(projectDetails.templateClasses, projectDetails.templateEnumerations, TreeNodeTag.Project), ...this.formatFoldersRecursively(projectDetails.folders, TreeNodeTag.Project, projectDetails.id)],\n      tag: TreeNodeTag.Project,\n      icon: GoJsNodeIcon.Project,\n      supportingNodes: [GojsNodeCategory.Class, GojsNodeCategory.AssociativeClass, GojsNodeCategory.Enumeration, GojsNodeCategory.Folder, GojsNodeCategory.Diagram]\n    };\n    return treeNode;\n  }\n  formatFoldersRecursively(folders, parentTag, projectId) {\n    return folders.map(folder => {\n      const children = [...this.formatClassAndEnum(folder.templateClasses, folder.templateEnumerations, `atTag${GojsNodeCategory.Folder}_${folder.id}`), ...this.sortTreeNodeChildren(this.formatFoldersRecursively(folder.childFolders || [], `atTag${GojsNodeCategory.Folder}_${folder.id}`, projectId))];\n      // Add diagrams to children only if their length is greater than zero\n      if (folder.diagrams.length > 0) {\n        children.unshift(this.formatDiagramTreeNode(folder.diagrams, `atTag${GojsNodeCategory.Folder}_${folder.id}`, projectId));\n      }\n      return {\n        name: folder.name,\n        children: children,\n        category: GojsNodeCategory.Folder,\n        icon: GoJsNodeIcon.Folder,\n        data: this.dataFormatService.formatFolderData(folder),\n        tag: `atTag${GojsNodeCategory.Folder}_${folder.id}`,\n        parentTag: parentTag,\n        isDraggable: true,\n        supportingNodes: [GojsNodeCategory.Class, GojsNodeCategory.AssociativeClass, GojsNodeCategory.Enumeration, GojsNodeCategory.Folder, GojsNodeCategory.Diagram]\n      };\n    });\n  }\n  formatClassAndEnum(templateClasses, templateEnumerations, parentTag) {\n    const treeNodes = [];\n    if (templateClasses.length > 0) {\n      treeNodes.push({\n        name: 'Classes',\n        children: this.sortTreeNodeChildren(templateClasses.map(tempClass => ({\n          name: tempClass.name,\n          children: this.sortTreeNodeChildren(this.formatAttributeNode(tempClass)),\n          category: tempClass.isAssociative ? GojsNodeCategory.AssociativeClass : GojsNodeCategory.Class,\n          tag: `atTag${GojsNodeCategory.Class}_${tempClass.id}`,\n          icon: tempClass.isAssociative ? GoJsNodeIcon.Associative : GoJsNodeIcon.Class,\n          parentTag: `${TreeNodeTag.ClassWrapper}_${parentTag}`,\n          data: this.dataFormatService.formatDiagramClassNode(tempClass, this.dataFormatService.formatAttributeData(tempClass.attributes || [])),\n          isDraggable: true,\n          supportingNodes: [GojsNodeCategory.Operation, GojsNodeCategory.Attribute]\n        }))),\n        supportingNodes: [GojsNodeCategory.Class, GojsNodeCategory.AssociativeClass],\n        category: ClassWrapperCategory,\n        tag: `${TreeNodeTag.ClassWrapper}_${parentTag}`,\n        parentTag: parentTag,\n        icon: GoJsNodeIcon.Class\n      });\n    }\n    if (templateEnumerations.length > 0) {\n      treeNodes.push({\n        name: 'Enumerations',\n        children: this.sortTreeNodeChildren(this.getTemplateEnumsForTree(templateEnumerations, parentTag)),\n        category: EnumWrapperCategory,\n        icon: GoJsNodeIcon.Enumeration,\n        tag: `${TreeNodeTag.EnumerationWrapper}_${parentTag}`,\n        parentTag: parentTag,\n        supportingNodes: [GojsNodeCategory.Enumeration]\n      });\n    }\n    return treeNodes;\n  }\n  formatDiagramTreeNode(diagrams, parentTag, projectId) {\n    return {\n      name: DiagramWrapperName,\n      children: diagrams.map(diagram => ({\n        name: diagram.name,\n        children: [],\n        category: GojsNodeCategory.Diagram,\n        icon: GoJsNodeIcon.Diagram,\n        tag: `atTag${GojsNodeCategory.Diagram}_${diagram.id}`,\n        data: {\n          ...diagram,\n          idProject: projectId\n        },\n        parentTag: `${TreeNodeTag.DiagramWrapper}_${parentTag}`,\n        isDraggable: true\n      })),\n      tag: `${TreeNodeTag.DiagramWrapper}_${parentTag}`,\n      parentTag: parentTag,\n      category: DiagramWrapperCategory,\n      icon: GoJsNodeIcon.Diagram,\n      supportingNodes: [GojsNodeCategory.Diagram]\n    };\n  }\n  getTemplateEnumsForTree(templateEnumerations, parentTag) {\n    return templateEnumerations.map(tempEnum => {\n      // add AttributeTypes for each tempEnum\n      this._diagramUtils.addAttributeTypes({\n        id: tempEnum.id?.toString(),\n        name: tempEnum?.name,\n        isEnumeration: true\n      });\n      // Return the formatted tree node for each tempEnum\n      return {\n        name: tempEnum.name,\n        children: this.formatLiteralTreeNode(tempEnum),\n        category: GojsNodeCategory.Enumeration,\n        tag: `atTag${GojsNodeCategory.Enumeration}_${tempEnum.id}`,\n        parentTag: `${TreeNodeTag.EnumerationWrapper}_${parentTag}`,\n        icon: GoJsNodeIcon.Enumeration,\n        data: this.dataFormatService.formatDiagramEnumData(tempEnum, this.dataFormatService.formatLiteralData(tempEnum.enumerationLiterals || [])),\n        isDraggable: true,\n        supportingNodes: [GojsNodeCategory.EnumerationLiteral]\n      };\n    });\n  }\n  formatLiteralTreeNode(tempEnum) {\n    return tempEnum.enumerationLiterals?.map(literal => ({\n      name: literal.name,\n      children: [],\n      category: GojsNodeCategory.EnumerationLiteral,\n      icon: GoJsNodeIcon.EnumerationLiteral,\n      tag: `atTag${GojsNodeCategory.EnumerationLiteral}_${literal.id}`,\n      parentTag: `atTag${GojsNodeCategory.Enumeration}_${tempEnum.id}`,\n      supportingNodes: [],\n      data: {\n        ...this.dataFormatService.formatLiteralData([literal])[0],\n        idTemplateEnumeration: tempEnum.id\n      }\n    })) || [];\n  }\n  formatAttributeNode(tempClass) {\n    return tempClass.attributes.map(attr => ({\n      name: attr.name,\n      children: [],\n      category: attr.category == AttributeMemberType.attribute ? GojsNodeCategory.Attribute : GojsNodeCategory.Operation,\n      icon: attr.category == AttributeMemberType.attribute ? GoJsNodeIcon.Attribute : GoJsNodeIcon.Operation,\n      tag: `atTag${attr.category == AttributeMemberType.attribute ? GojsNodeCategory.Attribute : GojsNodeCategory.Operation}_${attr.id}`,\n      parentTag: `atTag${GojsNodeCategory.Class}_${tempClass.id}`,\n      data: {\n        ...this.dataFormatService.formatAttributeData([attr])[0],\n        idTemplateClass: tempClass.id\n      },\n      supportingNodes: []\n    }));\n  }\n  /**\n   * Adds a group node to the tree structure by either appending it to a parent node\n   * or creating a wrapper node if a suitable parent is not found. The method handles\n   * nodes based on their category, organizing `Folder` nodes separately from other types.\n   * After insertion, the tree nodes are sorted to maintain a defined order.\n   * @param nodeData - The data for the node to be added to the tree.\n   *                   It contains the node's details, such as category and parent tag.\n   */\n  addGroupNodeInTree(nodeData) {\n    const treeNode = this.libraryDetails.getValue();\n    if (!treeNode) return;\n    const isProjectOrFolderNode = nodeData.category === GojsNodeCategory.Folder && nodeData.parentTag === TreeNodeTag.Project;\n    if (isProjectOrFolderNode) {\n      this.addNodeToChildren(nodeData, treeNode.children);\n    } else {\n      const parentNode = this.findNodeByTag(nodeData.parentTag);\n      if (parentNode) {\n        this.addNodeToParent(nodeData, parentNode);\n      } else {\n        // If parent not found, add to root\n        this.getOrCreateWrapperNode(nodeData, treeNode);\n      }\n    }\n    // Ensure the tree structure is sorted and updated\n    treeNode.children = this.sortTreeNodes(treeNode.children);\n    this.libraryDetails.next(treeNode);\n  }\n  addNodeToChildren(node, children) {\n    children.push(node);\n    this.sortTreeNodeChildren(children);\n  }\n  addNodeToParent(node, parentNode) {\n    if (parentNode.category === GojsNodeCategory.Folder) {\n      if (node.category !== GojsNodeCategory.Folder) {\n        this.getOrCreateWrapperNode(node, parentNode);\n      } else {\n        this.addNodeToChildren(node, parentNode.children);\n      }\n    } else {\n      this.addNodeToChildren(node, parentNode.children);\n    }\n  }\n  /**\n   * Sorts an array of tree nodes based on the priority of their categories.\n   * The priority order is defined in `CATEGORY_PRIORITY`. Nodes with undefined\n   * categories are placed at the end of the list.\n   *\n   * @param nodes - An array of `TreeNode` objects to be sorted.\n   * @returns A sorted array of `TreeNode` objects, ordered by their category priority.\n   */\n  sortTreeNodes(nodes) {\n    return nodes.sort((a, b) => {\n      // Get the priority index of each node's category\n      const indexA = this.CATEGORY_PRIORITY.indexOf(a.category);\n      const indexB = this.CATEGORY_PRIORITY.indexOf(b.category);\n      // Place nodes with unknown categories at the end\n      if (indexA === -1) return 1;\n      if (indexB === -1) return -1;\n      // Sort by priority index in ascending order\n      return indexA - indexB;\n    });\n  }\n  sortTreeNodeChildren(nodes) {\n    return nodes.sort((a, b) => a.name.localeCompare(b.name, undefined, {\n      sensitivity: 'base'\n    }));\n  }\n  /**\n   * Ensures that a wrapper node exists for the specified `targetedNode` within the `parentNode`.\n   * If the wrapper node already exists, the `targetedNode` is added to its children.\n   * Otherwise, a new wrapper node is created, added to the `parentNode`, and the `targetedNode`\n   * is added as its child.\n   *\n   * @param targetedNode - The `TreeNode` that needs to be added to a wrapper node.\n   * @param parentNode - The parent `TreeNode` under which the wrapper node will be created or updated.\n   */\n  getOrCreateWrapperNode(targetedNode, parentNode) {\n    // Attempt to find an existing wrapper node within the parent's children\n    let wrapperNode = parentNode.children.find(node => node.tag == `${targetedNode.category == GojsNodeCategory.Class || targetedNode.category == GojsNodeCategory.AssociativeClass ? TreeNodeTag.ClassWrapper : targetedNode.category === GojsNodeCategory.Diagram ? TreeNodeTag.DiagramWrapper : TreeNodeTag.EnumerationWrapper}_${targetedNode.parentTag}`);\n    if (wrapperNode) {\n      // If the wrapper node exists, add the targeted node as its child\n      wrapperNode.children.push(targetedNode);\n      this.sortTreeNodeChildren(wrapperNode.children);\n    } else {\n      // Create a new wrapper node if it doesn't exist\n      wrapperNode = this.constructWrapperNode(targetedNode, parentNode.tag);\n      parentNode?.children.push({\n        ...wrapperNode,\n        children: this.sortTreeNodeChildren([...wrapperNode.children, targetedNode])\n      });\n      this.sortTreeNodes(parentNode.children);\n    }\n  }\n  addItemNodeInParent(itemNode) {\n    const treeNode = this.libraryDetails.getValue();\n    const parentNode = this.findNodeByTag(itemNode.parentTag);\n    if (parentNode) {\n      parentNode.data.items.push(itemNode.data);\n      parentNode.children.push(itemNode);\n      this.sortTreeNodeChildren(parentNode.children);\n      this.libraryDetails.next(treeNode);\n    }\n  }\n  editGroupTreeNode(treeNode) {\n    const updatedLibraryDetails = this.libraryDetails.getValue();\n    if (updatedLibraryDetails) {\n      const parentNode = this.findParentNode(treeNode.tag, updatedLibraryDetails);\n      if (parentNode) {\n        const nodeToUpdate = parentNode?.children.find(node => node.tag === treeNode.tag);\n        if (nodeToUpdate && nodeToUpdate.data && treeNode.data) {\n          nodeToUpdate.name = treeNode.data.name;\n          if (nodeToUpdate.category === GojsNodeCategory.Attribute || nodeToUpdate.category === GojsNodeCategory.Operation || nodeToUpdate.category === GojsNodeCategory.EnumerationLiteral) {\n            this.updateItemInClassOrEnum(parentNode, treeNode, updatedLibraryDetails);\n            if (nodeToUpdate.category == GojsNodeCategory.Attribute || nodeToUpdate.category === GojsNodeCategory.Operation) {\n              const itemNode = nodeToUpdate.data;\n              // (nodeToUpdate.data as GojsDiagramAttributeNode).dataType = (\n              //   treeNode.data as GojsDiagramAttributeNode\n              // ).dataType;\n              this.updateNodeData(nodeToUpdate.data, {\n                name: itemNode.name,\n                id: itemNode.id,\n                description: itemNode.description,\n                dataType: itemNode.dataType\n              });\n            } else {\n              const itemNode = nodeToUpdate.data;\n              this.updateNodeData(nodeToUpdate.data, {\n                name: itemNode.name,\n                id: itemNode.id\n              });\n            }\n          } else {\n            const classOrEnumNode = treeNode.data;\n            this.updateNodeData(nodeToUpdate.data, {\n              name: classOrEnumNode.name,\n              id: classOrEnumNode.id,\n              color: classOrEnumNode.color,\n              description: classOrEnumNode.description,\n              tag: classOrEnumNode.tag,\n              volumetry: classOrEnumNode.volumetry,\n              treeNodeTag: classOrEnumNode.treeNodeTag,\n              position: classOrEnumNode.position,\n              size: classOrEnumNode.size\n            });\n          }\n          this.sortTreeNodeChildren(parentNode?.children);\n          this.sortTreeNodes(parentNode?.children);\n          this.libraryDetails.next(updatedLibraryDetails);\n        }\n      }\n    }\n  }\n  updateNodeData(nodeToUpdate, treeNodeData) {\n    Object.keys(treeNodeData).forEach(key => {\n      if (key in nodeToUpdate) {\n        nodeToUpdate[key] = treeNodeData[key];\n      }\n    });\n  }\n  updateItemInClassOrEnum(groupNode, treeNode, libraryDetails) {\n    if (groupNode.data && treeNode.data) {\n      groupNode.data.items.forEach(item => {\n        if (item.id == treeNode.data?.id) {\n          Object.assign(item, treeNode.data);\n        }\n      });\n      this.libraryDetails.next(libraryDetails);\n    }\n  }\n  deleteGroupTreeNode(treeNode) {\n    const updatedLibraryDetails = this.libraryDetails.getValue();\n    const parentNode = this.findParentNode(treeNode.tag, updatedLibraryDetails);\n    if (parentNode) {\n      const index = parentNode?.children.findIndex(node => node.tag === treeNode.tag);\n      if (treeNode.category === GojsNodeCategory.Attribute || treeNode.category === GojsNodeCategory.Operation || treeNode.category === GojsNodeCategory.EnumerationLiteral) {\n        parentNode.data.items = parentNode.data.items.filter(item => item.id !== treeNode.data?.id);\n      }\n      if (index > -1) {\n        parentNode?.children.splice(index, 1);\n        if (parentNode.children.length == 0 && (parentNode.category == ClassWrapperCategory || parentNode.category == EnumWrapperCategory || parentNode.category == DiagramWrapperCategory)) {\n          const emptyWrapperParentNode = this.findParentNode(parentNode.tag, updatedLibraryDetails);\n          if (emptyWrapperParentNode) {\n            emptyWrapperParentNode.children = emptyWrapperParentNode.children.filter(child => child.tag !== parentNode.tag);\n          }\n        }\n        this.libraryDetails.next(updatedLibraryDetails);\n      }\n    }\n  }\n  moveNode(targetFolder, draggedNode) {\n    // Get the current value of library details\n    const updatedLibraryDetails = this.libraryDetails.getValue();\n    // Find and remove the node from its current parent's children array\n    const parentNode = this.findParentNode(draggedNode.tag, updatedLibraryDetails);\n    if (!this.checkDropValidation(targetFolder, draggedNode, parentNode, updatedLibraryDetails)) return;\n    if (parentNode) {\n      parentNode.children = parentNode.children.filter(child => child.tag !== draggedNode.tag);\n      if (parentNode.children.length == 0 && (parentNode.category == ClassWrapperCategory || parentNode.category == EnumWrapperCategory || parentNode.category == DiagramWrapperCategory)) {\n        const emptyWrapperParentNode = this.findParentNode(parentNode.tag, updatedLibraryDetails);\n        if (emptyWrapperParentNode) {\n          emptyWrapperParentNode.children = emptyWrapperParentNode.children.filter(child => child.tag !== parentNode.tag);\n        }\n      }\n    }\n    // Add the node to the target folder's children array\n    if (draggedNode.category === GojsNodeCategory.Folder || targetFolder.category == ClassWrapperCategory || targetFolder.category == EnumWrapperCategory || targetFolder.category == DiagramWrapperCategory) {\n      targetFolder.children.push({\n        ...draggedNode,\n        parentTag: targetFolder.tag\n      });\n      this.sortTreeNodeChildren(targetFolder.children);\n      this.sortTreeNodes(targetFolder.children);\n      this.moveNodeToFolder(targetFolder, draggedNode);\n    } else {\n      const targetFolderNode = this.findNodeByTag(targetFolder.tag);\n      if (targetFolderNode) {\n        const wrapperNode = this.constructWrapperNode(draggedNode, targetFolderNode.tag);\n        const targetedWrapperNode = targetFolderNode?.children.find(node => node.tag === wrapperNode.tag);\n        this.moveNodeToFolder(targetFolderNode, draggedNode);\n        if (targetedWrapperNode) {\n          targetedWrapperNode.children.push({\n            ...draggedNode,\n            parentTag: targetedWrapperNode.tag\n          });\n          this.sortTreeNodeChildren(targetedWrapperNode.children);\n        } else {\n          targetFolderNode.children.push({\n            ...wrapperNode,\n            children: [...wrapperNode.children, {\n              ...draggedNode,\n              parentTag: wrapperNode.tag\n            }],\n            parentTag: targetFolder.tag\n          });\n          this.sortTreeNodes(targetFolderNode.children);\n        }\n      }\n    }\n    // Update the Subject with the modified library details\n    this.libraryDetails.next(updatedLibraryDetails);\n  }\n  checkDropValidation(targetNode, draggedNode, parentNode, libraryDetails) {\n    if (parentNode?.parentTag === libraryDetails?.tag && targetNode.category === GojsNodeCategory.Project && draggedNode.category !== GojsNodeCategory.Folder) {\n      return false;\n    }\n    if (draggedNode.tag === targetNode.tag || draggedNode.tag == targetNode.parentTag || draggedNode.parentTag == targetNode.tag) return false;\n    if (targetNode.category === GojsNodeCategory.Diagram) return false;\n    // Check if the current parent is the dragged node\n    let currentParent = this.findParentNode(targetNode.tag, libraryDetails);\n    if ((targetNode.category == ClassWrapperCategory || targetNode.category == DiagramWrapperCategory || targetNode.category == EnumWrapperCategory) && currentParent?.category == GojsNodeCategory.Folder) {\n      return false;\n    }\n    while (currentParent) {\n      if (currentParent.tag === draggedNode.tag) {\n        return false; // Found an ancestor\n      }\n      currentParent = this.findParentNode(currentParent.tag, libraryDetails);\n    }\n    return true;\n  }\n  findParentNode(nodeTag, folder) {\n    if (folder.children) {\n      for (let child of folder.children) {\n        if (child.tag === nodeTag) {\n          return folder;\n        }\n        const node = this.findParentNode(nodeTag, child);\n        if (node) return node;\n      }\n    }\n    return null;\n  }\n  findNodeByTag(tag) {\n    if (tag == TreeNodeTag.Project) {\n      return this.libraryDetails.getValue();\n    }\n    return this.descendantTreeNodes?.find(node => node.tag == tag) || null;\n  }\n  constructWrapperNode(draggedNode, parentTag) {\n    const wrapperNodeName = draggedNode.category === GojsNodeCategory.Class || draggedNode.category === GojsNodeCategory.AssociativeClass ? 'Classes' : draggedNode.category === GojsNodeCategory.Diagram ? 'Diagrams' : 'Enumerations';\n    const wrapperNodeTag = draggedNode.category === GojsNodeCategory.Class || draggedNode.category === GojsNodeCategory.AssociativeClass ? TreeNodeTag.ClassWrapper : draggedNode.category === GojsNodeCategory.Diagram ? TreeNodeTag.DiagramWrapper : TreeNodeTag.EnumerationWrapper;\n    const wrapperNodeCategory = draggedNode.category === GojsNodeCategory.Class || draggedNode.category === GojsNodeCategory.AssociativeClass ? ClassWrapperCategory : draggedNode.category === GojsNodeCategory.Diagram ? DiagramWrapperCategory : EnumWrapperCategory;\n    const wrapperNodeIcon = draggedNode.category === GojsNodeCategory.Class || draggedNode.category === GojsNodeCategory.AssociativeClass ? GoJsNodeIcon.Class : draggedNode.category === GojsNodeCategory.Diagram ? GoJsNodeIcon.Diagram : GoJsNodeIcon.Enumeration;\n    return {\n      name: wrapperNodeName,\n      children: [],\n      category: wrapperNodeCategory,\n      tag: `${wrapperNodeTag}_${parentTag}`,\n      parentTag: parentTag,\n      icon: wrapperNodeIcon,\n      supportingNodes: [draggedNode.category]\n    };\n  }\n  moveNodeToFolder(targetNode, draggedNode) {\n    if (draggedNode.data) {\n      if (targetNode.category === GojsNodeCategory.Folder) {\n        if (draggedNode.category === GojsNodeCategory.Class) {\n          this._classService.moveTempClassToFolder({\n            id: (draggedNode?.data).idTemplateClass,\n            idFolder: (targetNode?.data).idFolder\n          }).subscribe();\n        } else if (draggedNode.category === GojsNodeCategory.Enumeration) {\n          this._enumerationService.moveTempEnumToFolder({\n            id: (draggedNode?.data).idTemplateEnumeration,\n            idFolder: (targetNode?.data).idFolder\n          }).subscribe();\n        } else if (draggedNode.category === GojsNodeCategory.Diagram) {\n          this._folderService.moveDiagramToFolder({\n            id: (draggedNode?.data).id,\n            idFolder: (targetNode?.data).idFolder\n          });\n        } else if (draggedNode.category === GojsNodeCategory.Folder) {\n          this._folderService.moveFolderToFolder({\n            id: (draggedNode?.data).idFolder,\n            parentFolderId: (targetNode?.data).idFolder\n          });\n        }\n      } else {\n        if (draggedNode.category === GojsNodeCategory.Class) {\n          this._classService.removeTempClassFromFolder((draggedNode?.data).idTemplateClass).subscribe();\n        } else if (draggedNode.category === GojsNodeCategory.Enumeration) {\n          this._enumerationService.removeTempEnumFromFolder((draggedNode?.data).idTemplateEnumeration).subscribe();\n        } else if (draggedNode.category === GojsNodeCategory.Diagram) {\n          this._folderService.removeDiagramFromFolder((draggedNode?.data).id);\n        } else if (draggedNode.category === GojsNodeCategory.Folder) {\n          this._folderService.removeFolderFromFolder((draggedNode?.data).idFolder);\n        }\n      }\n    }\n  }\n  getClassesEnumsFromFolder(folderNode) {\n    const nodes = [];\n    // Recursive function to collect classes, enums, and folders from the target folder\n    const collectChildNodes = node => {\n      node.children.forEach(child => {\n        if (child.category === ClassWrapperCategory || child.category === EnumWrapperCategory) {\n          nodes.push(...child.children);\n        } else if (child.category === GojsNodeCategory.Folder) {\n          collectChildNodes(child);\n        }\n      });\n    };\n    // Start collecting from the folder node\n    collectChildNodes(folderNode);\n    return nodes;\n  }\n  /**\n   * Deletes a diagram node by tag.\n   * @param {string} tag - Unique identifier for the node.\n   * @memberof TreeNodeService\n   * @returns {void}\n   */\n  deleteDiagram(tag) {\n    const node = this.findNodeByTag(tag);\n    if (node) this.deleteGroupTreeNode(node);\n  }\n  nodeExistOrNot(parentTag, nodes) {\n    const libraryDetails = this.libraryDetails.getValue();\n    if (libraryDetails) {\n      const parentNode = this.findParentNode(parentTag, libraryDetails);\n      if (parentNode) {\n        if (parentNode.category == GojsNodeCategory.Folder) {\n          if (nodes.some(node => node.tag === parentNode.tag)) return true;else return this.nodeExistOrNot(parentNode.tag, nodes);\n        }\n        return nodes.some(node => node.tag === parentNode.tag);\n      } else return false;\n    } else return false;\n  }\n  findCurrentDiagramParentNode() {\n    const currentDiagramTag = `atTag${GojsNodeCategory.Diagram}_${this.currentDiagramId}`;\n    const libraryDetails = this.libraryDetails.getValue();\n    if (libraryDetails) {\n      const wrapperNode = this.findParentNode(currentDiagramTag, libraryDetails);\n      if (wrapperNode) {\n        return this.findNodeByTag(wrapperNode.parentTag);\n      } else return null;\n    } else return null;\n  }\n  static #_ = this.ɵfac = function TreeNodeService_Factory(t) {\n    return new (t || TreeNodeService)(i0.ɵɵinject(i1.DataFormatService), i0.ɵɵinject(i2.ClassService), i0.ɵɵinject(i3.EnumerationService), i0.ɵɵinject(i4.FolderService), i0.ɵɵinject(i5.DiagramUtils));\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: TreeNodeService,\n    factory: TreeNodeService.ɵfac,\n    providedIn: 'root'\n  });\n}", "map": {"version": 3, "names": ["computed", "signal", "BehaviorSubject", "AttributeMemberType", "GoJsNodeIcon", "GojsNodeCategory", "TreeNodeTag", "ClassWrapperCategory", "DiagramWrapperCategory", "DiagramWrapperName", "EnumWrapperCategory", "TreeNodeService", "constructor", "dataFormatService", "_classService", "_enumerationService", "_folderService", "_diagramUtils", "_libraryDetails", "libraryDetails", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "libraryDetailsSubject", "_descendantTreeNodes", "currentDiagramId", "descendantNodes", "root", "nodes", "collectNodes", "nodesList", "for<PERSON>ach", "node", "push", "children", "CATEGORY_PRIORITY", "Folder", "activeDiagramChanges", "subscribe", "diagram", "id", "getLibraryDetails", "asObservable", "setLibraryDetails", "projectDetails", "formattedNode", "formatTreeData", "next", "descendantTreeNodes", "getWrapperParentTag", "wrapperTag", "Project", "treeNode", "name", "category", "diagrams", "length", "formatDiagramTreeNode", "formatClassAndEnum", "templateClasses", "templateEnumerations", "formatFoldersRecursively", "folders", "tag", "icon", "supportingNodes", "Class", "AssociativeClass", "Enumeration", "Diagram", "parentTag", "projectId", "map", "folder", "sortTreeNodeChildren", "childFolders", "unshift", "data", "formatFolderData", "isDraggable", "treeNodes", "tempClass", "formatAttributeNode", "isAssociative", "Associative", "ClassWrapper", "formatDiagramClassNode", "formatAttributeData", "attributes", "Operation", "Attribute", "getTemplateEnumsForTree", "EnumerationWrapper", "idProject", "DiagramWrapper", "tempEnum", "addAttributeTypes", "toString", "isEnumeration", "formatLiteralTreeNode", "formatDiagramEnumData", "formatLiteralData", "enumerationLiterals", "EnumerationLiteral", "literal", "idTemplateEnumeration", "attr", "attribute", "idTemplateClass", "addGroupNodeInTree", "nodeData", "getValue", "isProjectOrFolderNode", "addNodeToChildren", "parentNode", "findNodeByTag", "addNodeToParent", "getOrCreateWrapperNode", "sortTreeNodes", "sort", "a", "b", "indexA", "indexOf", "indexB", "localeCompare", "undefined", "sensitivity", "targetedNode", "wrapperNode", "find", "constructWrapperNode", "addItemNodeInParent", "itemNode", "items", "editGroupTreeNode", "updatedLibraryDetails", "findParentNode", "nodeToUpdate", "updateItemInClassOrEnum", "updateNodeData", "description", "dataType", "classOrEnumNode", "color", "volumetry", "treeNodeTag", "position", "size", "treeNodeData", "Object", "keys", "key", "groupNode", "item", "assign", "deleteGroupTreeNode", "index", "findIndex", "filter", "splice", "emptyWrapperParentNode", "child", "moveNode", "targetFolder", "draggedNode", "checkDropValidation", "moveNodeToFolder", "targetFolderNode", "targetedWrapperNode", "targetNode", "currentParent", "nodeTag", "wrapperNodeName", "wrapperNodeTag", "wrapperNodeCategory", "wrapperNodeIcon", "moveTempClassToFolder", "idFolder", "moveTempEnumToFolder", "moveDiagramToFolder", "moveFolderToFolder", "parentFolderId", "removeTempClassFromFolder", "removeTempEnumFromFolder", "removeDiagramFromFolder", "removeFolderFromFolder", "getClassesEnumsFromFolder", "folderNode", "collectChildNodes", "deleteDiagram", "nodeExistOrNot", "some", "findCurrentDiagramParentNode", "currentDiagramTag", "_", "i0", "ɵɵinject", "i1", "DataFormatService", "i2", "ClassService", "i3", "EnumerationService", "i4", "FolderService", "i5", "DiagramUtils", "_2", "factory", "ɵfac", "providedIn"], "sources": ["D:\\GitHub\\Bassetti\\devint-BASSETTI-GROUP-APP\\BassettiUMLWebApp\\UMLApp\\src\\app\\core\\services\\treeNode\\tree-node.service.ts"], "sourcesContent": ["import { computed, Injectable, signal } from '@angular/core';\r\nimport { BehaviorSubject } from 'rxjs';\r\nimport { AttributeMemberType } from 'src/app/shared/model/attribute';\r\nimport { FolderDTO, TemplateClass } from 'src/app/shared/model/class';\r\nimport { GoJsNodeIcon } from 'src/app/shared/model/common';\r\nimport { Diagram } from 'src/app/shared/model/diagram';\r\nimport { TemplateEnumeration } from 'src/app/shared/model/enumeration';\r\nimport {\r\n  GojsDiagramAttributeNode,\r\n  GojsDiagramClassNode,\r\n  GojsDiagramEnumerationNode,\r\n  GojsDiagramLiteralNode,\r\n  GojsFolderNode,\r\n  GojsNodeCategory,\r\n} from 'src/app/shared/model/gojs';\r\nimport { ProjectDetails } from 'src/app/shared/model/project';\r\nimport { TreeNode, TreeNodeTag } from 'src/app/shared/model/treeNode';\r\nimport {\r\n  ClassWrapperCategory,\r\n  DiagramWrapperCategory,\r\n  DiagramWrapperName,\r\n  EnumWrapperCategory,\r\n} from 'src/app/shared/utils/constants';\r\nimport { DiagramUtils } from 'src/app/shared/utils/diagram-utils';\r\nimport { ClassService } from '../class/class.service';\r\nimport { DataFormatService } from '../data-format/data-format.service';\r\nimport { EnumerationService } from '../enumeration/enumeration.service';\r\nimport { FolderService } from '../folder/folder.service';\r\n\r\n@Injectable({\r\n  providedIn: 'root',\r\n})\r\nexport class TreeNodeService {\r\n  // Convert to signals\r\n  private _libraryDetails = signal<TreeNode | null>(null);\r\n  public readonly libraryDetails = this._libraryDetails.asReadonly();\r\n\r\n  // Keep BehaviorSubject for backward compatibility during transition\r\n  private libraryDetailsSubject = new BehaviorSubject<TreeNode | null>(null);\r\n\r\n  private _descendantTreeNodes: TreeNode[] | null = [];\r\n  private currentDiagramId: number = -1;\r\n\r\n  // Computed signals for derived data\r\n  public readonly descendantNodes = computed(() => {\r\n    const root = this._libraryDetails();\r\n    if (!root) return [];\r\n\r\n    const nodes: TreeNode[] = [];\r\n    const collectNodes = (nodesList: TreeNode[]) => {\r\n      nodesList.forEach((node) => {\r\n        nodes.push(node);\r\n        if (node.children) {\r\n          collectNodes(node.children);\r\n        }\r\n      });\r\n    };\r\n    collectNodes([root]);\r\n    return nodes;\r\n  });\r\n  CATEGORY_PRIORITY = [\r\n    DiagramWrapperCategory,\r\n    ClassWrapperCategory,\r\n    EnumWrapperCategory,\r\n    GojsNodeCategory.Folder, // Always at the end\r\n  ];\r\n  constructor(\r\n    private dataFormatService: DataFormatService,\r\n    private _classService: ClassService,\r\n    private _enumerationService: EnumerationService,\r\n    private _folderService: FolderService,\r\n    private _diagramUtils: DiagramUtils\r\n  ) {\r\n    this._diagramUtils.activeDiagramChanges().subscribe((diagram) => {\r\n      if (diagram && diagram.id) this.currentDiagramId = diagram.id;\r\n    });\r\n  }\r\n  getLibraryDetails() {\r\n    return this.libraryDetails.asObservable();\r\n  }\r\n  setLibraryDetails(projectDetails: ProjectDetails) {\r\n    const formattedNode = this.formatTreeData(projectDetails);\r\n    return this.libraryDetails.next(formattedNode);\r\n  }\r\n  get descendantTreeNodes(): TreeNode[] | null {\r\n    return this._descendantTreeNodes;\r\n  }\r\n\r\n  set descendantTreeNodes(nodes: TreeNode[]) {\r\n    this._descendantTreeNodes = nodes;\r\n  }\r\n\r\n  getWrapperParentTag(wrapperTag: string) {\r\n    return `${wrapperTag}_${TreeNodeTag.Project}`;\r\n  }\r\n\r\n  formatTreeData(projectDetails: ProjectDetails): TreeNode {\r\n    const treeNode = {\r\n      name: projectDetails.name,\r\n      category: GojsNodeCategory.Project,\r\n      children: [\r\n        ...(projectDetails.diagrams.length > 0\r\n          ? [\r\n              this.formatDiagramTreeNode(\r\n                projectDetails.diagrams,\r\n                TreeNodeTag.Project,\r\n                projectDetails.id!\r\n              ),\r\n            ]\r\n          : []),\r\n        ...this.formatClassAndEnum(\r\n          projectDetails.templateClasses,\r\n          projectDetails.templateEnumerations,\r\n          TreeNodeTag.Project\r\n        ),\r\n        ...this.formatFoldersRecursively(\r\n          projectDetails.folders,\r\n          TreeNodeTag.Project,\r\n          projectDetails.id!\r\n        ),\r\n      ],\r\n      tag: TreeNodeTag.Project,\r\n      icon: GoJsNodeIcon.Project,\r\n      supportingNodes: [\r\n        GojsNodeCategory.Class,\r\n        GojsNodeCategory.AssociativeClass,\r\n        GojsNodeCategory.Enumeration,\r\n        GojsNodeCategory.Folder,\r\n        GojsNodeCategory.Diagram,\r\n      ],\r\n    };\r\n    return treeNode;\r\n  }\r\n\r\n  private formatFoldersRecursively(\r\n    folders: FolderDTO[],\r\n    parentTag: string,\r\n    projectId: number\r\n  ): TreeNode[] {\r\n    return folders.map((folder) => {\r\n      const children: TreeNode[] = [\r\n        ...this.formatClassAndEnum(\r\n          folder.templateClasses!,\r\n          folder.templateEnumerations!,\r\n          `atTag${GojsNodeCategory.Folder}_${folder.id}`\r\n        ),\r\n        ...this.sortTreeNodeChildren(\r\n          this.formatFoldersRecursively(\r\n            folder.childFolders || [],\r\n            `atTag${GojsNodeCategory.Folder}_${folder.id}`,\r\n            projectId\r\n          )\r\n        ),\r\n      ];\r\n\r\n      // Add diagrams to children only if their length is greater than zero\r\n      if (folder.diagrams.length > 0) {\r\n        children.unshift(\r\n          this.formatDiagramTreeNode(\r\n            folder.diagrams,\r\n            `atTag${GojsNodeCategory.Folder}_${folder.id}`,\r\n            projectId\r\n          )\r\n        );\r\n      }\r\n\r\n      return {\r\n        name: folder.name,\r\n        children: children,\r\n        category: GojsNodeCategory.Folder,\r\n        icon: GoJsNodeIcon.Folder,\r\n        data: this.dataFormatService.formatFolderData(folder),\r\n        tag: `atTag${GojsNodeCategory.Folder}_${folder.id}`,\r\n        parentTag: parentTag,\r\n        isDraggable: true,\r\n        supportingNodes: [\r\n          GojsNodeCategory.Class,\r\n          GojsNodeCategory.AssociativeClass,\r\n          GojsNodeCategory.Enumeration,\r\n          GojsNodeCategory.Folder,\r\n          GojsNodeCategory.Diagram,\r\n        ],\r\n      };\r\n    });\r\n  }\r\n\r\n  private formatClassAndEnum(\r\n    templateClasses: TemplateClass[],\r\n    templateEnumerations: TemplateEnumeration[],\r\n    parentTag: string\r\n  ): TreeNode[] {\r\n    const treeNodes: TreeNode[] = [];\r\n    if (templateClasses.length > 0) {\r\n      treeNodes.push({\r\n        name: 'Classes',\r\n        children: this.sortTreeNodeChildren(\r\n          templateClasses.map((tempClass) => ({\r\n            name: tempClass.name,\r\n            children: this.sortTreeNodeChildren(\r\n              this.formatAttributeNode(tempClass)\r\n            ),\r\n            category: tempClass.isAssociative\r\n              ? GojsNodeCategory.AssociativeClass\r\n              : GojsNodeCategory.Class,\r\n            tag: `atTag${GojsNodeCategory.Class}_${tempClass.id}`,\r\n            icon: tempClass.isAssociative\r\n              ? GoJsNodeIcon.Associative\r\n              : GoJsNodeIcon.Class,\r\n            parentTag: `${TreeNodeTag.ClassWrapper}_${parentTag}`,\r\n            data: this.dataFormatService.formatDiagramClassNode(\r\n              tempClass,\r\n              this.dataFormatService.formatAttributeData(\r\n                tempClass.attributes || []\r\n              )\r\n            ),\r\n            isDraggable: true,\r\n            supportingNodes: [\r\n              GojsNodeCategory.Operation,\r\n              GojsNodeCategory.Attribute,\r\n            ],\r\n          }))\r\n        ),\r\n        supportingNodes: [\r\n          GojsNodeCategory.Class,\r\n          GojsNodeCategory.AssociativeClass,\r\n        ],\r\n        category: ClassWrapperCategory,\r\n        tag: `${TreeNodeTag.ClassWrapper}_${parentTag}`,\r\n        parentTag: parentTag,\r\n        icon: GoJsNodeIcon.Class,\r\n      });\r\n    }\r\n    if (templateEnumerations.length > 0) {\r\n      treeNodes.push({\r\n        name: 'Enumerations',\r\n        children: this.sortTreeNodeChildren(\r\n          this.getTemplateEnumsForTree(templateEnumerations, parentTag)\r\n        ),\r\n        category: EnumWrapperCategory,\r\n        icon: GoJsNodeIcon.Enumeration,\r\n        tag: `${TreeNodeTag.EnumerationWrapper}_${parentTag}`,\r\n        parentTag: parentTag,\r\n        supportingNodes: [GojsNodeCategory.Enumeration],\r\n      });\r\n    }\r\n    return treeNodes;\r\n  }\r\n\r\n  private formatDiagramTreeNode(\r\n    diagrams: Diagram[],\r\n    parentTag: string,\r\n    projectId: number\r\n  ): TreeNode {\r\n    return {\r\n      name: DiagramWrapperName,\r\n      children: diagrams.map((diagram) => ({\r\n        name: diagram.name,\r\n        children: [],\r\n        category: GojsNodeCategory.Diagram,\r\n        icon: GoJsNodeIcon.Diagram,\r\n        tag: `atTag${GojsNodeCategory.Diagram}_${diagram.id}`,\r\n        data: { ...diagram, idProject: projectId },\r\n        parentTag: `${TreeNodeTag.DiagramWrapper}_${parentTag}`,\r\n        isDraggable: true,\r\n      })),\r\n      tag: `${TreeNodeTag.DiagramWrapper}_${parentTag}`,\r\n      parentTag: parentTag,\r\n      category: DiagramWrapperCategory,\r\n      icon: GoJsNodeIcon.Diagram,\r\n      supportingNodes: [GojsNodeCategory.Diagram],\r\n    };\r\n  }\r\n\r\n  private getTemplateEnumsForTree(\r\n    templateEnumerations: TemplateEnumeration[],\r\n    parentTag: string\r\n  ): TreeNode[] {\r\n    return templateEnumerations.map((tempEnum) => {\r\n      // add AttributeTypes for each tempEnum\r\n      this._diagramUtils.addAttributeTypes({\r\n        id: tempEnum.id?.toString()!,\r\n        name: tempEnum?.name,\r\n        isEnumeration: true,\r\n      });\r\n\r\n      // Return the formatted tree node for each tempEnum\r\n      return {\r\n        name: tempEnum.name,\r\n        children: this.formatLiteralTreeNode(tempEnum),\r\n        category: GojsNodeCategory.Enumeration,\r\n        tag: `atTag${GojsNodeCategory.Enumeration}_${tempEnum.id}`,\r\n        parentTag: `${TreeNodeTag.EnumerationWrapper}_${parentTag}`,\r\n        icon: GoJsNodeIcon.Enumeration,\r\n        data: this.dataFormatService.formatDiagramEnumData(\r\n          tempEnum,\r\n          this.dataFormatService.formatLiteralData(\r\n            tempEnum.enumerationLiterals || []\r\n          )\r\n        ),\r\n        isDraggable: true,\r\n        supportingNodes: [GojsNodeCategory.EnumerationLiteral],\r\n      };\r\n    });\r\n  }\r\n\r\n  private formatLiteralTreeNode(tempEnum: TemplateEnumeration): TreeNode[] {\r\n    return (\r\n      tempEnum.enumerationLiterals?.map((literal) => ({\r\n        name: literal.name,\r\n        children: [],\r\n        category: GojsNodeCategory.EnumerationLiteral,\r\n        icon: GoJsNodeIcon.EnumerationLiteral,\r\n        tag: `atTag${GojsNodeCategory.EnumerationLiteral}_${literal.id}`,\r\n        parentTag: `atTag${GojsNodeCategory.Enumeration}_${tempEnum.id}`,\r\n        supportingNodes: [],\r\n        data: {\r\n          ...this.dataFormatService.formatLiteralData([literal])[0],\r\n          idTemplateEnumeration: tempEnum.id,\r\n        },\r\n      })) || []\r\n    );\r\n  }\r\n\r\n  private formatAttributeNode(tempClass: TemplateClass): TreeNode[] {\r\n    return tempClass.attributes.map((attr) => ({\r\n      name: attr.name,\r\n      children: [],\r\n      category:\r\n        attr.category == AttributeMemberType.attribute\r\n          ? GojsNodeCategory.Attribute\r\n          : GojsNodeCategory.Operation,\r\n      icon:\r\n        attr.category == AttributeMemberType.attribute\r\n          ? GoJsNodeIcon.Attribute\r\n          : GoJsNodeIcon.Operation,\r\n      tag: `atTag${\r\n        attr.category == AttributeMemberType.attribute\r\n          ? GojsNodeCategory.Attribute\r\n          : GojsNodeCategory.Operation\r\n      }_${attr.id}`,\r\n      parentTag: `atTag${GojsNodeCategory.Class}_${tempClass.id}`,\r\n      data: {\r\n        ...this.dataFormatService.formatAttributeData([attr])[0],\r\n        idTemplateClass: tempClass.id,\r\n      },\r\n      supportingNodes: [],\r\n    }));\r\n  }\r\n\r\n  /**\r\n   * Adds a group node to the tree structure by either appending it to a parent node\r\n   * or creating a wrapper node if a suitable parent is not found. The method handles\r\n   * nodes based on their category, organizing `Folder` nodes separately from other types.\r\n   * After insertion, the tree nodes are sorted to maintain a defined order.\r\n   * @param nodeData - The data for the node to be added to the tree.\r\n   *                   It contains the node's details, such as category and parent tag.\r\n   */\r\n  addGroupNodeInTree(nodeData: TreeNode): void {\r\n    const treeNode = this.libraryDetails.getValue();\r\n    if (!treeNode) return;\r\n    const isProjectOrFolderNode =\r\n      nodeData.category === GojsNodeCategory.Folder &&\r\n      nodeData.parentTag === TreeNodeTag.Project;\r\n    if (isProjectOrFolderNode) {\r\n      this.addNodeToChildren(nodeData, treeNode.children);\r\n    } else {\r\n      const parentNode = this.findNodeByTag(nodeData.parentTag!);\r\n      if (parentNode) {\r\n        this.addNodeToParent(nodeData, parentNode);\r\n      } else {\r\n        // If parent not found, add to root\r\n        this.getOrCreateWrapperNode(nodeData, treeNode);\r\n      }\r\n    }\r\n    // Ensure the tree structure is sorted and updated\r\n    treeNode.children = this.sortTreeNodes(treeNode.children);\r\n    this.libraryDetails.next(treeNode);\r\n  }\r\n\r\n  private addNodeToChildren(node: TreeNode, children: TreeNode[]): void {\r\n    children.push(node);\r\n    this.sortTreeNodeChildren(children);\r\n  }\r\n\r\n  private addNodeToParent(node: TreeNode, parentNode: TreeNode): void {\r\n    if (parentNode.category === GojsNodeCategory.Folder) {\r\n      if (node.category !== GojsNodeCategory.Folder) {\r\n        this.getOrCreateWrapperNode(node, parentNode);\r\n      } else {\r\n        this.addNodeToChildren(node, parentNode.children);\r\n      }\r\n    } else {\r\n      this.addNodeToChildren(node, parentNode.children);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Sorts an array of tree nodes based on the priority of their categories.\r\n   * The priority order is defined in `CATEGORY_PRIORITY`. Nodes with undefined\r\n   * categories are placed at the end of the list.\r\n   *\r\n   * @param nodes - An array of `TreeNode` objects to be sorted.\r\n   * @returns A sorted array of `TreeNode` objects, ordered by their category priority.\r\n   */\r\n  private sortTreeNodes(nodes: TreeNode[]): TreeNode[] {\r\n    return nodes.sort((a, b) => {\r\n      // Get the priority index of each node's category\r\n      const indexA = this.CATEGORY_PRIORITY.indexOf(a.category);\r\n      const indexB = this.CATEGORY_PRIORITY.indexOf(b.category);\r\n\r\n      // Place nodes with unknown categories at the end\r\n      if (indexA === -1) return 1;\r\n      if (indexB === -1) return -1;\r\n\r\n      // Sort by priority index in ascending order\r\n      return indexA - indexB;\r\n    });\r\n  }\r\n\r\n  private sortTreeNodeChildren(nodes: TreeNode[]): TreeNode[] {\r\n    return nodes.sort((a, b) =>\r\n      a.name.localeCompare(b.name, undefined, { sensitivity: 'base' })\r\n    );\r\n  }\r\n  /**\r\n   * Ensures that a wrapper node exists for the specified `targetedNode` within the `parentNode`.\r\n   * If the wrapper node already exists, the `targetedNode` is added to its children.\r\n   * Otherwise, a new wrapper node is created, added to the `parentNode`, and the `targetedNode`\r\n   * is added as its child.\r\n   *\r\n   * @param targetedNode - The `TreeNode` that needs to be added to a wrapper node.\r\n   * @param parentNode - The parent `TreeNode` under which the wrapper node will be created or updated.\r\n   */\r\n  private getOrCreateWrapperNode(\r\n    targetedNode: TreeNode,\r\n    parentNode: TreeNode\r\n  ): void {\r\n    // Attempt to find an existing wrapper node within the parent's children\r\n    let wrapperNode = parentNode.children.find(\r\n      (node) =>\r\n        node.tag ==\r\n        `${\r\n          targetedNode.category == GojsNodeCategory.Class ||\r\n          targetedNode.category == GojsNodeCategory.AssociativeClass\r\n            ? TreeNodeTag.ClassWrapper\r\n            : targetedNode.category === GojsNodeCategory.Diagram\r\n            ? TreeNodeTag.DiagramWrapper\r\n            : TreeNodeTag.EnumerationWrapper\r\n        }_${targetedNode.parentTag}`\r\n    );\r\n\r\n    if (wrapperNode) {\r\n      // If the wrapper node exists, add the targeted node as its child\r\n      wrapperNode.children.push(targetedNode);\r\n      this.sortTreeNodeChildren(wrapperNode.children);\r\n    } else {\r\n      // Create a new wrapper node if it doesn't exist\r\n      wrapperNode = this.constructWrapperNode(targetedNode, parentNode.tag);\r\n      parentNode?.children.push({\r\n        ...wrapperNode,\r\n        children: this.sortTreeNodeChildren([\r\n          ...wrapperNode.children,\r\n          targetedNode,\r\n        ]),\r\n      });\r\n      this.sortTreeNodes(parentNode.children);\r\n    }\r\n  }\r\n\r\n  addItemNodeInParent(itemNode: TreeNode) {\r\n    const treeNode = this.libraryDetails.getValue();\r\n    const parentNode = this.findNodeByTag(itemNode.parentTag!);\r\n    if (parentNode) {\r\n      (\r\n        parentNode.data as GojsDiagramClassNode | GojsDiagramEnumerationNode\r\n      ).items.push(\r\n        itemNode.data as GojsDiagramAttributeNode | GojsDiagramLiteralNode\r\n      );\r\n      parentNode.children.push(itemNode);\r\n      this.sortTreeNodeChildren(parentNode.children);\r\n      this.libraryDetails.next(treeNode);\r\n    }\r\n  }\r\n\r\n  editGroupTreeNode(treeNode: TreeNode) {\r\n    const updatedLibraryDetails = this.libraryDetails.getValue();\r\n    if (updatedLibraryDetails) {\r\n      const parentNode = this.findParentNode(\r\n        treeNode.tag,\r\n        updatedLibraryDetails\r\n      );\r\n      if (parentNode) {\r\n        const nodeToUpdate = parentNode?.children.find(\r\n          (node) => node.tag === treeNode.tag\r\n        );\r\n        if (nodeToUpdate && nodeToUpdate.data && treeNode.data) {\r\n          nodeToUpdate.name = treeNode.data.name;\r\n          if (\r\n            nodeToUpdate.category === GojsNodeCategory.Attribute ||\r\n            nodeToUpdate.category === GojsNodeCategory.Operation ||\r\n            nodeToUpdate.category === GojsNodeCategory.EnumerationLiteral\r\n          ) {\r\n            this.updateItemInClassOrEnum(\r\n              parentNode,\r\n              treeNode,\r\n              updatedLibraryDetails\r\n            );\r\n            if (\r\n              nodeToUpdate.category == GojsNodeCategory.Attribute ||\r\n              nodeToUpdate.category === GojsNodeCategory.Operation\r\n            ) {\r\n              const itemNode = nodeToUpdate.data as GojsDiagramAttributeNode;\r\n              // (nodeToUpdate.data as GojsDiagramAttributeNode).dataType = (\r\n              //   treeNode.data as GojsDiagramAttributeNode\r\n              // ).dataType;\r\n              this.updateNodeData(nodeToUpdate.data, {\r\n                name: itemNode.name,\r\n                id: itemNode.id,\r\n                description: itemNode.description,\r\n                dataType: itemNode.dataType,\r\n              });\r\n            } else {\r\n              const itemNode = nodeToUpdate.data as GojsDiagramLiteralNode;\r\n              this.updateNodeData(nodeToUpdate.data, {\r\n                name: itemNode.name,\r\n                id: itemNode.id,\r\n              });\r\n            }\r\n          } else {\r\n            const classOrEnumNode = treeNode.data as\r\n              | GojsDiagramClassNode\r\n              | GojsDiagramEnumerationNode;\r\n            this.updateNodeData(nodeToUpdate.data, {\r\n              name: classOrEnumNode.name,\r\n              id: classOrEnumNode.id,\r\n              color: classOrEnumNode.color,\r\n              description: classOrEnumNode.description,\r\n              tag: classOrEnumNode.tag,\r\n              volumetry: classOrEnumNode.volumetry,\r\n              treeNodeTag: classOrEnumNode.treeNodeTag,\r\n              position: classOrEnumNode.position,\r\n              size: classOrEnumNode.size,\r\n            });\r\n          }\r\n          this.sortTreeNodeChildren(parentNode?.children);\r\n          this.sortTreeNodes(parentNode?.children);\r\n          this.libraryDetails.next(updatedLibraryDetails);\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  private updateNodeData<T extends object>(\r\n    nodeToUpdate: T,\r\n    treeNodeData: Partial<T>\r\n  ): void {\r\n    Object.keys(treeNodeData).forEach((key) => {\r\n      if (key in nodeToUpdate) {\r\n        (nodeToUpdate as any)[key] = (treeNodeData as any)[key];\r\n      }\r\n    });\r\n  }\r\n\r\n  updateItemInClassOrEnum(\r\n    groupNode: TreeNode,\r\n    treeNode: TreeNode,\r\n    libraryDetails: TreeNode\r\n  ) {\r\n    if (groupNode.data && treeNode.data) {\r\n      (\r\n        groupNode.data as GojsDiagramClassNode | GojsDiagramEnumerationNode\r\n      ).items.forEach((item) => {\r\n        if (item.id == treeNode.data?.id) {\r\n          Object.assign(item, treeNode.data);\r\n        }\r\n      });\r\n      this.libraryDetails.next(libraryDetails);\r\n    }\r\n  }\r\n\r\n  deleteGroupTreeNode(treeNode: TreeNode) {\r\n    const updatedLibraryDetails = this.libraryDetails.getValue();\r\n    const parentNode = this.findParentNode(\r\n      treeNode.tag,\r\n      updatedLibraryDetails!\r\n    );\r\n    if (parentNode) {\r\n      const index = parentNode?.children.findIndex(\r\n        (node) => node.tag === treeNode.tag\r\n      );\r\n      if (\r\n        treeNode.category === GojsNodeCategory.Attribute ||\r\n        treeNode.category === GojsNodeCategory.Operation ||\r\n        treeNode.category === GojsNodeCategory.EnumerationLiteral\r\n      ) {\r\n        (\r\n          parentNode.data as GojsDiagramClassNode | GojsDiagramEnumerationNode\r\n        ).items = (\r\n          parentNode.data as GojsDiagramClassNode | GojsDiagramEnumerationNode\r\n        ).items.filter((item) => item.id !== treeNode.data?.id);\r\n      }\r\n      if (index > -1) {\r\n        parentNode?.children.splice(index, 1);\r\n        if (\r\n          parentNode.children.length == 0 &&\r\n          (parentNode.category == ClassWrapperCategory ||\r\n            parentNode.category == EnumWrapperCategory ||\r\n            parentNode.category == DiagramWrapperCategory)\r\n        ) {\r\n          const emptyWrapperParentNode = this.findParentNode(\r\n            parentNode.tag,\r\n            updatedLibraryDetails!\r\n          );\r\n          if (emptyWrapperParentNode) {\r\n            emptyWrapperParentNode.children =\r\n              emptyWrapperParentNode.children.filter(\r\n                (child: TreeNode) => child.tag !== parentNode.tag\r\n              );\r\n          }\r\n        }\r\n        this.libraryDetails.next(updatedLibraryDetails);\r\n      }\r\n    }\r\n  }\r\n\r\n  moveNode(targetFolder: TreeNode, draggedNode: TreeNode) {\r\n    // Get the current value of library details\r\n    const updatedLibraryDetails = this.libraryDetails.getValue();\r\n    // Find and remove the node from its current parent's children array\r\n    const parentNode = this.findParentNode(\r\n      draggedNode.tag,\r\n      updatedLibraryDetails!\r\n    );\r\n\r\n    if (\r\n      !this.checkDropValidation(\r\n        targetFolder,\r\n        draggedNode,\r\n        parentNode,\r\n        updatedLibraryDetails!\r\n      )\r\n    )\r\n      return;\r\n\r\n    if (parentNode) {\r\n      parentNode.children = parentNode.children.filter(\r\n        (child: TreeNode) => child.tag !== draggedNode.tag\r\n      );\r\n      if (\r\n        parentNode.children.length == 0 &&\r\n        (parentNode.category == ClassWrapperCategory ||\r\n          parentNode.category == EnumWrapperCategory ||\r\n          parentNode.category == DiagramWrapperCategory)\r\n      ) {\r\n        const emptyWrapperParentNode = this.findParentNode(\r\n          parentNode.tag,\r\n          updatedLibraryDetails!\r\n        );\r\n        if (emptyWrapperParentNode) {\r\n          emptyWrapperParentNode.children =\r\n            emptyWrapperParentNode.children.filter(\r\n              (child: TreeNode) => child.tag !== parentNode.tag\r\n            );\r\n        }\r\n      }\r\n    }\r\n    // Add the node to the target folder's children array\r\n\r\n    if (\r\n      draggedNode.category === GojsNodeCategory.Folder ||\r\n      targetFolder.category == ClassWrapperCategory ||\r\n      targetFolder.category == EnumWrapperCategory ||\r\n      targetFolder.category == DiagramWrapperCategory\r\n    ) {\r\n      targetFolder.children.push({\r\n        ...draggedNode,\r\n        parentTag: targetFolder.tag,\r\n      });\r\n      this.sortTreeNodeChildren(targetFolder.children);\r\n      this.sortTreeNodes(targetFolder.children);\r\n      this.moveNodeToFolder(targetFolder, draggedNode);\r\n    } else {\r\n      const targetFolderNode = this.findNodeByTag(targetFolder.tag);\r\n      if (targetFolderNode) {\r\n        const wrapperNode = this.constructWrapperNode(\r\n          draggedNode,\r\n          targetFolderNode.tag\r\n        );\r\n        const targetedWrapperNode = targetFolderNode?.children.find(\r\n          (node: TreeNode) => node.tag === wrapperNode.tag\r\n        );\r\n        this.moveNodeToFolder(targetFolderNode, draggedNode);\r\n        if (targetedWrapperNode) {\r\n          targetedWrapperNode.children.push({\r\n            ...draggedNode,\r\n            parentTag: targetedWrapperNode.tag,\r\n          });\r\n\r\n          this.sortTreeNodeChildren(targetedWrapperNode.children);\r\n        } else {\r\n          targetFolderNode.children.push({\r\n            ...wrapperNode,\r\n            children: [\r\n              ...wrapperNode.children,\r\n              { ...draggedNode, parentTag: wrapperNode.tag },\r\n            ],\r\n            parentTag: targetFolder.tag,\r\n          });\r\n          this.sortTreeNodes(targetFolderNode.children);\r\n        }\r\n      }\r\n    }\r\n    // Update the Subject with the modified library details\r\n    this.libraryDetails.next(updatedLibraryDetails);\r\n  }\r\n\r\n  private checkDropValidation(\r\n    targetNode: TreeNode,\r\n    draggedNode: TreeNode,\r\n    parentNode: TreeNode | null,\r\n    libraryDetails: TreeNode\r\n  ): boolean {\r\n    if (\r\n      parentNode?.parentTag === libraryDetails?.tag &&\r\n      targetNode.category === GojsNodeCategory.Project &&\r\n      draggedNode.category !== GojsNodeCategory.Folder\r\n    ) {\r\n      return false;\r\n    }\r\n    if (\r\n      draggedNode.tag === targetNode.tag ||\r\n      draggedNode.tag == targetNode.parentTag ||\r\n      draggedNode.parentTag == targetNode.tag\r\n    )\r\n      return false;\r\n\r\n    if (targetNode.category === GojsNodeCategory.Diagram) return false;\r\n\r\n    // Check if the current parent is the dragged node\r\n    let currentParent = this.findParentNode(targetNode.tag, libraryDetails);\r\n    if (\r\n      (targetNode.category == ClassWrapperCategory ||\r\n        targetNode.category == DiagramWrapperCategory ||\r\n        targetNode.category == EnumWrapperCategory) &&\r\n      currentParent?.category == GojsNodeCategory.Folder\r\n    ) {\r\n      return false;\r\n    }\r\n    while (currentParent) {\r\n      if (currentParent.tag === draggedNode.tag) {\r\n        return false; // Found an ancestor\r\n      }\r\n      currentParent = this.findParentNode(currentParent.tag, libraryDetails);\r\n    }\r\n    return true;\r\n  }\r\n\r\n  findParentNode(nodeTag: string, folder: TreeNode): TreeNode | null {\r\n    if (folder.children) {\r\n      for (let child of folder.children) {\r\n        if (child.tag === nodeTag) {\r\n          return folder;\r\n        }\r\n        const node = this.findParentNode(nodeTag, child);\r\n        if (node) return node;\r\n      }\r\n    }\r\n    return null;\r\n  }\r\n\r\n  findNodeByTag(tag: string): TreeNode | null {\r\n    if (tag == TreeNodeTag.Project) {\r\n      return this.libraryDetails.getValue();\r\n    }\r\n    return this.descendantTreeNodes?.find((node) => node.tag == tag) || null;\r\n  }\r\n\r\n  private constructWrapperNode(\r\n    draggedNode: TreeNode,\r\n    parentTag: string\r\n  ): TreeNode {\r\n    const wrapperNodeName =\r\n      draggedNode.category === GojsNodeCategory.Class ||\r\n      draggedNode.category === GojsNodeCategory.AssociativeClass\r\n        ? 'Classes'\r\n        : draggedNode.category === GojsNodeCategory.Diagram\r\n        ? 'Diagrams'\r\n        : 'Enumerations';\r\n    const wrapperNodeTag =\r\n      draggedNode.category === GojsNodeCategory.Class ||\r\n      draggedNode.category === GojsNodeCategory.AssociativeClass\r\n        ? TreeNodeTag.ClassWrapper\r\n        : draggedNode.category === GojsNodeCategory.Diagram\r\n        ? TreeNodeTag.DiagramWrapper\r\n        : TreeNodeTag.EnumerationWrapper;\r\n    const wrapperNodeCategory =\r\n      draggedNode.category === GojsNodeCategory.Class ||\r\n      draggedNode.category === GojsNodeCategory.AssociativeClass\r\n        ? ClassWrapperCategory\r\n        : draggedNode.category === GojsNodeCategory.Diagram\r\n        ? DiagramWrapperCategory\r\n        : EnumWrapperCategory;\r\n    const wrapperNodeIcon =\r\n      draggedNode.category === GojsNodeCategory.Class ||\r\n      draggedNode.category === GojsNodeCategory.AssociativeClass\r\n        ? GoJsNodeIcon.Class\r\n        : draggedNode.category === GojsNodeCategory.Diagram\r\n        ? GoJsNodeIcon.Diagram\r\n        : GoJsNodeIcon.Enumeration;\r\n    return {\r\n      name: wrapperNodeName,\r\n      children: [],\r\n      category: wrapperNodeCategory,\r\n      tag: `${wrapperNodeTag}_${parentTag}`,\r\n      parentTag: parentTag,\r\n      icon: wrapperNodeIcon,\r\n      supportingNodes: [draggedNode.category],\r\n    };\r\n  }\r\n\r\n  private moveNodeToFolder(targetNode: TreeNode, draggedNode: TreeNode) {\r\n    if (draggedNode.data) {\r\n      if (targetNode.category === GojsNodeCategory.Folder) {\r\n        if (draggedNode.category === GojsNodeCategory.Class) {\r\n          this._classService\r\n            .moveTempClassToFolder({\r\n              id: (draggedNode?.data as GojsDiagramClassNode).idTemplateClass,\r\n              idFolder: (targetNode?.data as GojsFolderNode).idFolder!,\r\n            })\r\n            .subscribe();\r\n        } else if (draggedNode.category === GojsNodeCategory.Enumeration) {\r\n          this._enumerationService\r\n            .moveTempEnumToFolder({\r\n              id: (draggedNode?.data as GojsDiagramEnumerationNode)\r\n                .idTemplateEnumeration,\r\n              idFolder: (targetNode?.data as GojsFolderNode).idFolder!,\r\n            })\r\n            .subscribe();\r\n        } else if (draggedNode.category === GojsNodeCategory.Diagram) {\r\n          this._folderService.moveDiagramToFolder({\r\n            id: (draggedNode?.data as Diagram).id!,\r\n            idFolder: (targetNode?.data as GojsFolderNode).idFolder!,\r\n          });\r\n        } else if (draggedNode.category === GojsNodeCategory.Folder) {\r\n          this._folderService.moveFolderToFolder({\r\n            id: (draggedNode?.data as GojsFolderNode).idFolder!,\r\n            parentFolderId: (targetNode?.data as GojsFolderNode).idFolder!,\r\n          });\r\n        }\r\n      } else {\r\n        if (draggedNode.category === GojsNodeCategory.Class) {\r\n          this._classService\r\n            .removeTempClassFromFolder(\r\n              (draggedNode?.data as GojsDiagramClassNode).idTemplateClass\r\n            )\r\n            .subscribe();\r\n        } else if (draggedNode.category === GojsNodeCategory.Enumeration) {\r\n          this._enumerationService\r\n            .removeTempEnumFromFolder(\r\n              (draggedNode?.data as GojsDiagramEnumerationNode)\r\n                .idTemplateEnumeration\r\n            )\r\n            .subscribe();\r\n        } else if (draggedNode.category === GojsNodeCategory.Diagram) {\r\n          this._folderService.removeDiagramFromFolder(\r\n            (draggedNode?.data as Diagram).id!\r\n          );\r\n        } else if (draggedNode.category === GojsNodeCategory.Folder) {\r\n          this._folderService.removeFolderFromFolder(\r\n            (draggedNode?.data as GojsFolderNode).idFolder!\r\n          );\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  getClassesEnumsFromFolder(folderNode: TreeNode): TreeNode[] {\r\n    const nodes: TreeNode[] = [];\r\n    // Recursive function to collect classes, enums, and folders from the target folder\r\n    const collectChildNodes = (node: TreeNode) => {\r\n      node.children.forEach((child) => {\r\n        if (\r\n          child.category === ClassWrapperCategory ||\r\n          child.category === EnumWrapperCategory\r\n        ) {\r\n          nodes.push(...child.children);\r\n        } else if (child.category === GojsNodeCategory.Folder) {\r\n          collectChildNodes(child);\r\n        }\r\n      });\r\n    };\r\n    // Start collecting from the folder node\r\n    collectChildNodes(folderNode);\r\n    return nodes;\r\n  }\r\n\r\n  /**\r\n   * Deletes a diagram node by tag.\r\n   * @param {string} tag - Unique identifier for the node.\r\n   * @memberof TreeNodeService\r\n   * @returns {void}\r\n   */\r\n  deleteDiagram(tag: string): void {\r\n    const node = this.findNodeByTag(tag);\r\n    if (node) this.deleteGroupTreeNode(node);\r\n  }\r\n\r\n  nodeExistOrNot(parentTag: string, nodes: TreeNode[]): boolean {\r\n    const libraryDetails = this.libraryDetails.getValue();\r\n    if (libraryDetails) {\r\n      const parentNode = this.findParentNode(parentTag, libraryDetails);\r\n      if (parentNode) {\r\n        if (parentNode.category == GojsNodeCategory.Folder) {\r\n          if (nodes.some((node) => node.tag === parentNode.tag)) return true;\r\n          else return this.nodeExistOrNot(parentNode.tag, nodes);\r\n        }\r\n        return nodes.some((node) => node.tag === parentNode.tag);\r\n      } else return false;\r\n    } else return false;\r\n  }\r\n\r\n  findCurrentDiagramParentNode(): TreeNode | null {\r\n    const currentDiagramTag = `atTag${GojsNodeCategory.Diagram}_${this.currentDiagramId}`;\r\n    const libraryDetails = this.libraryDetails.getValue();\r\n    if (libraryDetails) {\r\n      const wrapperNode = this.findParentNode(\r\n        currentDiagramTag,\r\n        libraryDetails\r\n      );\r\n      if (wrapperNode) {\r\n        return this.findNodeByTag(wrapperNode.parentTag!);\r\n      } else return null;\r\n    } else return null;\r\n  }\r\n\r\n  // expandNode(tag: string): void {\r\n  //   const node = this.findNodeByTag(tag);\r\n  //   if (node) {\r\n  //     node.isExpanded = true;\r\n  //     this.libraryDetails.next(this.libraryDetails.getValue());\r\n  //   }\r\n  // }\r\n}\r\n"], "mappings": "AAAA,SAASA,QAAQ,EAAcC,MAAM,QAAQ,eAAe;AAC5D,SAASC,eAAe,QAAQ,MAAM;AACtC,SAASC,mBAAmB,QAAQ,gCAAgC;AAEpE,SAASC,YAAY,QAAQ,6BAA6B;AAG1D,SAMEC,gBAAgB,QACX,2BAA2B;AAElC,SAAmBC,WAAW,QAAQ,+BAA+B;AACrE,SACEC,oBAAoB,EACpBC,sBAAsB,EACtBC,kBAAkB,EAClBC,mBAAmB,QACd,gCAAgC;;;;;;;AAUvC,OAAM,MAAOC,eAAe;EAkC1BC,YACUC,iBAAoC,EACpCC,aAA2B,EAC3BC,mBAAuC,EACvCC,cAA6B,EAC7BC,aAA2B;IAJ3B,KAAAJ,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,mBAAmB,GAAnBA,mBAAmB;IACnB,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,aAAa,GAAbA,aAAa;IAtCvB;IACQ,KAAAC,eAAe,GAAGjB,MAAM,CAAkB,IAAI,CAAC;IACvC,KAAAkB,cAAc,GAAG,IAAI,CAACD,eAAe,CAACE,UAAU,EAAE;IAElE;IACQ,KAAAC,qBAAqB,GAAG,IAAInB,eAAe,CAAkB,IAAI,CAAC;IAElE,KAAAoB,oBAAoB,GAAsB,EAAE;IAC5C,KAAAC,gBAAgB,GAAW,CAAC,CAAC;IAErC;IACgB,KAAAC,eAAe,GAAGxB,QAAQ,CAAC,MAAK;MAC9C,MAAMyB,IAAI,GAAG,IAAI,CAACP,eAAe,EAAE;MACnC,IAAI,CAACO,IAAI,EAAE,OAAO,EAAE;MAEpB,MAAMC,KAAK,GAAe,EAAE;MAC5B,MAAMC,YAAY,GAAIC,SAAqB,IAAI;QAC7CA,SAAS,CAACC,OAAO,CAAEC,IAAI,IAAI;UACzBJ,KAAK,CAACK,IAAI,CAACD,IAAI,CAAC;UAChB,IAAIA,IAAI,CAACE,QAAQ,EAAE;YACjBL,YAAY,CAACG,IAAI,CAACE,QAAQ,CAAC;;QAE/B,CAAC,CAAC;MACJ,CAAC;MACDL,YAAY,CAAC,CAACF,IAAI,CAAC,CAAC;MACpB,OAAOC,KAAK;IACd,CAAC,CAAC;IACF,KAAAO,iBAAiB,GAAG,CAClBzB,sBAAsB,EACtBD,oBAAoB,EACpBG,mBAAmB,EACnBL,gBAAgB,CAAC6B,MAAM,CAAE;IAAA,CAC1B;IAQC,IAAI,CAACjB,aAAa,CAACkB,oBAAoB,EAAE,CAACC,SAAS,CAAEC,OAAO,IAAI;MAC9D,IAAIA,OAAO,IAAIA,OAAO,CAACC,EAAE,EAAE,IAAI,CAACf,gBAAgB,GAAGc,OAAO,CAACC,EAAE;IAC/D,CAAC,CAAC;EACJ;EACAC,iBAAiBA,CAAA;IACf,OAAO,IAAI,CAACpB,cAAc,CAACqB,YAAY,EAAE;EAC3C;EACAC,iBAAiBA,CAACC,cAA8B;IAC9C,MAAMC,aAAa,GAAG,IAAI,CAACC,cAAc,CAACF,cAAc,CAAC;IACzD,OAAO,IAAI,CAACvB,cAAc,CAAC0B,IAAI,CAACF,aAAa,CAAC;EAChD;EACA,IAAIG,mBAAmBA,CAAA;IACrB,OAAO,IAAI,CAACxB,oBAAoB;EAClC;EAEA,IAAIwB,mBAAmBA,CAACpB,KAAiB;IACvC,IAAI,CAACJ,oBAAoB,GAAGI,KAAK;EACnC;EAEAqB,mBAAmBA,CAACC,UAAkB;IACpC,OAAO,GAAGA,UAAU,IAAI1C,WAAW,CAAC2C,OAAO,EAAE;EAC/C;EAEAL,cAAcA,CAACF,cAA8B;IAC3C,MAAMQ,QAAQ,GAAG;MACfC,IAAI,EAAET,cAAc,CAACS,IAAI;MACzBC,QAAQ,EAAE/C,gBAAgB,CAAC4C,OAAO;MAClCjB,QAAQ,EAAE,CACR,IAAIU,cAAc,CAACW,QAAQ,CAACC,MAAM,GAAG,CAAC,GAClC,CACE,IAAI,CAACC,qBAAqB,CACxBb,cAAc,CAACW,QAAQ,EACvB/C,WAAW,CAAC2C,OAAO,EACnBP,cAAc,CAACJ,EAAG,CACnB,CACF,GACD,EAAE,CAAC,EACP,GAAG,IAAI,CAACkB,kBAAkB,CACxBd,cAAc,CAACe,eAAe,EAC9Bf,cAAc,CAACgB,oBAAoB,EACnCpD,WAAW,CAAC2C,OAAO,CACpB,EACD,GAAG,IAAI,CAACU,wBAAwB,CAC9BjB,cAAc,CAACkB,OAAO,EACtBtD,WAAW,CAAC2C,OAAO,EACnBP,cAAc,CAACJ,EAAG,CACnB,CACF;MACDuB,GAAG,EAAEvD,WAAW,CAAC2C,OAAO;MACxBa,IAAI,EAAE1D,YAAY,CAAC6C,OAAO;MAC1Bc,eAAe,EAAE,CACf1D,gBAAgB,CAAC2D,KAAK,EACtB3D,gBAAgB,CAAC4D,gBAAgB,EACjC5D,gBAAgB,CAAC6D,WAAW,EAC5B7D,gBAAgB,CAAC6B,MAAM,EACvB7B,gBAAgB,CAAC8D,OAAO;KAE3B;IACD,OAAOjB,QAAQ;EACjB;EAEQS,wBAAwBA,CAC9BC,OAAoB,EACpBQ,SAAiB,EACjBC,SAAiB;IAEjB,OAAOT,OAAO,CAACU,GAAG,CAAEC,MAAM,IAAI;MAC5B,MAAMvC,QAAQ,GAAe,CAC3B,GAAG,IAAI,CAACwB,kBAAkB,CACxBe,MAAM,CAACd,eAAgB,EACvBc,MAAM,CAACb,oBAAqB,EAC5B,QAAQrD,gBAAgB,CAAC6B,MAAM,IAAIqC,MAAM,CAACjC,EAAE,EAAE,CAC/C,EACD,GAAG,IAAI,CAACkC,oBAAoB,CAC1B,IAAI,CAACb,wBAAwB,CAC3BY,MAAM,CAACE,YAAY,IAAI,EAAE,EACzB,QAAQpE,gBAAgB,CAAC6B,MAAM,IAAIqC,MAAM,CAACjC,EAAE,EAAE,EAC9C+B,SAAS,CACV,CACF,CACF;MAED;MACA,IAAIE,MAAM,CAAClB,QAAQ,CAACC,MAAM,GAAG,CAAC,EAAE;QAC9BtB,QAAQ,CAAC0C,OAAO,CACd,IAAI,CAACnB,qBAAqB,CACxBgB,MAAM,CAAClB,QAAQ,EACf,QAAQhD,gBAAgB,CAAC6B,MAAM,IAAIqC,MAAM,CAACjC,EAAE,EAAE,EAC9C+B,SAAS,CACV,CACF;;MAGH,OAAO;QACLlB,IAAI,EAAEoB,MAAM,CAACpB,IAAI;QACjBnB,QAAQ,EAAEA,QAAQ;QAClBoB,QAAQ,EAAE/C,gBAAgB,CAAC6B,MAAM;QACjC4B,IAAI,EAAE1D,YAAY,CAAC8B,MAAM;QACzByC,IAAI,EAAE,IAAI,CAAC9D,iBAAiB,CAAC+D,gBAAgB,CAACL,MAAM,CAAC;QACrDV,GAAG,EAAE,QAAQxD,gBAAgB,CAAC6B,MAAM,IAAIqC,MAAM,CAACjC,EAAE,EAAE;QACnD8B,SAAS,EAAEA,SAAS;QACpBS,WAAW,EAAE,IAAI;QACjBd,eAAe,EAAE,CACf1D,gBAAgB,CAAC2D,KAAK,EACtB3D,gBAAgB,CAAC4D,gBAAgB,EACjC5D,gBAAgB,CAAC6D,WAAW,EAC5B7D,gBAAgB,CAAC6B,MAAM,EACvB7B,gBAAgB,CAAC8D,OAAO;OAE3B;IACH,CAAC,CAAC;EACJ;EAEQX,kBAAkBA,CACxBC,eAAgC,EAChCC,oBAA2C,EAC3CU,SAAiB;IAEjB,MAAMU,SAAS,GAAe,EAAE;IAChC,IAAIrB,eAAe,CAACH,MAAM,GAAG,CAAC,EAAE;MAC9BwB,SAAS,CAAC/C,IAAI,CAAC;QACboB,IAAI,EAAE,SAAS;QACfnB,QAAQ,EAAE,IAAI,CAACwC,oBAAoB,CACjCf,eAAe,CAACa,GAAG,CAAES,SAAS,KAAM;UAClC5B,IAAI,EAAE4B,SAAS,CAAC5B,IAAI;UACpBnB,QAAQ,EAAE,IAAI,CAACwC,oBAAoB,CACjC,IAAI,CAACQ,mBAAmB,CAACD,SAAS,CAAC,CACpC;UACD3B,QAAQ,EAAE2B,SAAS,CAACE,aAAa,GAC7B5E,gBAAgB,CAAC4D,gBAAgB,GACjC5D,gBAAgB,CAAC2D,KAAK;UAC1BH,GAAG,EAAE,QAAQxD,gBAAgB,CAAC2D,KAAK,IAAIe,SAAS,CAACzC,EAAE,EAAE;UACrDwB,IAAI,EAAEiB,SAAS,CAACE,aAAa,GACzB7E,YAAY,CAAC8E,WAAW,GACxB9E,YAAY,CAAC4D,KAAK;UACtBI,SAAS,EAAE,GAAG9D,WAAW,CAAC6E,YAAY,IAAIf,SAAS,EAAE;UACrDO,IAAI,EAAE,IAAI,CAAC9D,iBAAiB,CAACuE,sBAAsB,CACjDL,SAAS,EACT,IAAI,CAAClE,iBAAiB,CAACwE,mBAAmB,CACxCN,SAAS,CAACO,UAAU,IAAI,EAAE,CAC3B,CACF;UACDT,WAAW,EAAE,IAAI;UACjBd,eAAe,EAAE,CACf1D,gBAAgB,CAACkF,SAAS,EAC1BlF,gBAAgB,CAACmF,SAAS;SAE7B,CAAC,CAAC,CACJ;QACDzB,eAAe,EAAE,CACf1D,gBAAgB,CAAC2D,KAAK,EACtB3D,gBAAgB,CAAC4D,gBAAgB,CAClC;QACDb,QAAQ,EAAE7C,oBAAoB;QAC9BsD,GAAG,EAAE,GAAGvD,WAAW,CAAC6E,YAAY,IAAIf,SAAS,EAAE;QAC/CA,SAAS,EAAEA,SAAS;QACpBN,IAAI,EAAE1D,YAAY,CAAC4D;OACpB,CAAC;;IAEJ,IAAIN,oBAAoB,CAACJ,MAAM,GAAG,CAAC,EAAE;MACnCwB,SAAS,CAAC/C,IAAI,CAAC;QACboB,IAAI,EAAE,cAAc;QACpBnB,QAAQ,EAAE,IAAI,CAACwC,oBAAoB,CACjC,IAAI,CAACiB,uBAAuB,CAAC/B,oBAAoB,EAAEU,SAAS,CAAC,CAC9D;QACDhB,QAAQ,EAAE1C,mBAAmB;QAC7BoD,IAAI,EAAE1D,YAAY,CAAC8D,WAAW;QAC9BL,GAAG,EAAE,GAAGvD,WAAW,CAACoF,kBAAkB,IAAItB,SAAS,EAAE;QACrDA,SAAS,EAAEA,SAAS;QACpBL,eAAe,EAAE,CAAC1D,gBAAgB,CAAC6D,WAAW;OAC/C,CAAC;;IAEJ,OAAOY,SAAS;EAClB;EAEQvB,qBAAqBA,CAC3BF,QAAmB,EACnBe,SAAiB,EACjBC,SAAiB;IAEjB,OAAO;MACLlB,IAAI,EAAE1C,kBAAkB;MACxBuB,QAAQ,EAAEqB,QAAQ,CAACiB,GAAG,CAAEjC,OAAO,KAAM;QACnCc,IAAI,EAAEd,OAAO,CAACc,IAAI;QAClBnB,QAAQ,EAAE,EAAE;QACZoB,QAAQ,EAAE/C,gBAAgB,CAAC8D,OAAO;QAClCL,IAAI,EAAE1D,YAAY,CAAC+D,OAAO;QAC1BN,GAAG,EAAE,QAAQxD,gBAAgB,CAAC8D,OAAO,IAAI9B,OAAO,CAACC,EAAE,EAAE;QACrDqC,IAAI,EAAE;UAAE,GAAGtC,OAAO;UAAEsD,SAAS,EAAEtB;QAAS,CAAE;QAC1CD,SAAS,EAAE,GAAG9D,WAAW,CAACsF,cAAc,IAAIxB,SAAS,EAAE;QACvDS,WAAW,EAAE;OACd,CAAC,CAAC;MACHhB,GAAG,EAAE,GAAGvD,WAAW,CAACsF,cAAc,IAAIxB,SAAS,EAAE;MACjDA,SAAS,EAAEA,SAAS;MACpBhB,QAAQ,EAAE5C,sBAAsB;MAChCsD,IAAI,EAAE1D,YAAY,CAAC+D,OAAO;MAC1BJ,eAAe,EAAE,CAAC1D,gBAAgB,CAAC8D,OAAO;KAC3C;EACH;EAEQsB,uBAAuBA,CAC7B/B,oBAA2C,EAC3CU,SAAiB;IAEjB,OAAOV,oBAAoB,CAACY,GAAG,CAAEuB,QAAQ,IAAI;MAC3C;MACA,IAAI,CAAC5E,aAAa,CAAC6E,iBAAiB,CAAC;QACnCxD,EAAE,EAAEuD,QAAQ,CAACvD,EAAE,EAAEyD,QAAQ,EAAG;QAC5B5C,IAAI,EAAE0C,QAAQ,EAAE1C,IAAI;QACpB6C,aAAa,EAAE;OAChB,CAAC;MAEF;MACA,OAAO;QACL7C,IAAI,EAAE0C,QAAQ,CAAC1C,IAAI;QACnBnB,QAAQ,EAAE,IAAI,CAACiE,qBAAqB,CAACJ,QAAQ,CAAC;QAC9CzC,QAAQ,EAAE/C,gBAAgB,CAAC6D,WAAW;QACtCL,GAAG,EAAE,QAAQxD,gBAAgB,CAAC6D,WAAW,IAAI2B,QAAQ,CAACvD,EAAE,EAAE;QAC1D8B,SAAS,EAAE,GAAG9D,WAAW,CAACoF,kBAAkB,IAAItB,SAAS,EAAE;QAC3DN,IAAI,EAAE1D,YAAY,CAAC8D,WAAW;QAC9BS,IAAI,EAAE,IAAI,CAAC9D,iBAAiB,CAACqF,qBAAqB,CAChDL,QAAQ,EACR,IAAI,CAAChF,iBAAiB,CAACsF,iBAAiB,CACtCN,QAAQ,CAACO,mBAAmB,IAAI,EAAE,CACnC,CACF;QACDvB,WAAW,EAAE,IAAI;QACjBd,eAAe,EAAE,CAAC1D,gBAAgB,CAACgG,kBAAkB;OACtD;IACH,CAAC,CAAC;EACJ;EAEQJ,qBAAqBA,CAACJ,QAA6B;IACzD,OACEA,QAAQ,CAACO,mBAAmB,EAAE9B,GAAG,CAAEgC,OAAO,KAAM;MAC9CnD,IAAI,EAAEmD,OAAO,CAACnD,IAAI;MAClBnB,QAAQ,EAAE,EAAE;MACZoB,QAAQ,EAAE/C,gBAAgB,CAACgG,kBAAkB;MAC7CvC,IAAI,EAAE1D,YAAY,CAACiG,kBAAkB;MACrCxC,GAAG,EAAE,QAAQxD,gBAAgB,CAACgG,kBAAkB,IAAIC,OAAO,CAAChE,EAAE,EAAE;MAChE8B,SAAS,EAAE,QAAQ/D,gBAAgB,CAAC6D,WAAW,IAAI2B,QAAQ,CAACvD,EAAE,EAAE;MAChEyB,eAAe,EAAE,EAAE;MACnBY,IAAI,EAAE;QACJ,GAAG,IAAI,CAAC9D,iBAAiB,CAACsF,iBAAiB,CAAC,CAACG,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;QACzDC,qBAAqB,EAAEV,QAAQ,CAACvD;;KAEnC,CAAC,CAAC,IAAI,EAAE;EAEb;EAEQ0C,mBAAmBA,CAACD,SAAwB;IAClD,OAAOA,SAAS,CAACO,UAAU,CAAChB,GAAG,CAAEkC,IAAI,KAAM;MACzCrD,IAAI,EAAEqD,IAAI,CAACrD,IAAI;MACfnB,QAAQ,EAAE,EAAE;MACZoB,QAAQ,EACNoD,IAAI,CAACpD,QAAQ,IAAIjD,mBAAmB,CAACsG,SAAS,GAC1CpG,gBAAgB,CAACmF,SAAS,GAC1BnF,gBAAgB,CAACkF,SAAS;MAChCzB,IAAI,EACF0C,IAAI,CAACpD,QAAQ,IAAIjD,mBAAmB,CAACsG,SAAS,GAC1CrG,YAAY,CAACoF,SAAS,GACtBpF,YAAY,CAACmF,SAAS;MAC5B1B,GAAG,EAAE,QACH2C,IAAI,CAACpD,QAAQ,IAAIjD,mBAAmB,CAACsG,SAAS,GAC1CpG,gBAAgB,CAACmF,SAAS,GAC1BnF,gBAAgB,CAACkF,SACvB,IAAIiB,IAAI,CAAClE,EAAE,EAAE;MACb8B,SAAS,EAAE,QAAQ/D,gBAAgB,CAAC2D,KAAK,IAAIe,SAAS,CAACzC,EAAE,EAAE;MAC3DqC,IAAI,EAAE;QACJ,GAAG,IAAI,CAAC9D,iBAAiB,CAACwE,mBAAmB,CAAC,CAACmB,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;QACxDE,eAAe,EAAE3B,SAAS,CAACzC;OAC5B;MACDyB,eAAe,EAAE;KAClB,CAAC,CAAC;EACL;EAEA;;;;;;;;EAQA4C,kBAAkBA,CAACC,QAAkB;IACnC,MAAM1D,QAAQ,GAAG,IAAI,CAAC/B,cAAc,CAAC0F,QAAQ,EAAE;IAC/C,IAAI,CAAC3D,QAAQ,EAAE;IACf,MAAM4D,qBAAqB,GACzBF,QAAQ,CAACxD,QAAQ,KAAK/C,gBAAgB,CAAC6B,MAAM,IAC7C0E,QAAQ,CAACxC,SAAS,KAAK9D,WAAW,CAAC2C,OAAO;IAC5C,IAAI6D,qBAAqB,EAAE;MACzB,IAAI,CAACC,iBAAiB,CAACH,QAAQ,EAAE1D,QAAQ,CAAClB,QAAQ,CAAC;KACpD,MAAM;MACL,MAAMgF,UAAU,GAAG,IAAI,CAACC,aAAa,CAACL,QAAQ,CAACxC,SAAU,CAAC;MAC1D,IAAI4C,UAAU,EAAE;QACd,IAAI,CAACE,eAAe,CAACN,QAAQ,EAAEI,UAAU,CAAC;OAC3C,MAAM;QACL;QACA,IAAI,CAACG,sBAAsB,CAACP,QAAQ,EAAE1D,QAAQ,CAAC;;;IAGnD;IACAA,QAAQ,CAAClB,QAAQ,GAAG,IAAI,CAACoF,aAAa,CAAClE,QAAQ,CAAClB,QAAQ,CAAC;IACzD,IAAI,CAACb,cAAc,CAAC0B,IAAI,CAACK,QAAQ,CAAC;EACpC;EAEQ6D,iBAAiBA,CAACjF,IAAc,EAAEE,QAAoB;IAC5DA,QAAQ,CAACD,IAAI,CAACD,IAAI,CAAC;IACnB,IAAI,CAAC0C,oBAAoB,CAACxC,QAAQ,CAAC;EACrC;EAEQkF,eAAeA,CAACpF,IAAc,EAAEkF,UAAoB;IAC1D,IAAIA,UAAU,CAAC5D,QAAQ,KAAK/C,gBAAgB,CAAC6B,MAAM,EAAE;MACnD,IAAIJ,IAAI,CAACsB,QAAQ,KAAK/C,gBAAgB,CAAC6B,MAAM,EAAE;QAC7C,IAAI,CAACiF,sBAAsB,CAACrF,IAAI,EAAEkF,UAAU,CAAC;OAC9C,MAAM;QACL,IAAI,CAACD,iBAAiB,CAACjF,IAAI,EAAEkF,UAAU,CAAChF,QAAQ,CAAC;;KAEpD,MAAM;MACL,IAAI,CAAC+E,iBAAiB,CAACjF,IAAI,EAAEkF,UAAU,CAAChF,QAAQ,CAAC;;EAErD;EAEA;;;;;;;;EAQQoF,aAAaA,CAAC1F,KAAiB;IACrC,OAAOA,KAAK,CAAC2F,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAI;MACzB;MACA,MAAMC,MAAM,GAAG,IAAI,CAACvF,iBAAiB,CAACwF,OAAO,CAACH,CAAC,CAAClE,QAAQ,CAAC;MACzD,MAAMsE,MAAM,GAAG,IAAI,CAACzF,iBAAiB,CAACwF,OAAO,CAACF,CAAC,CAACnE,QAAQ,CAAC;MAEzD;MACA,IAAIoE,MAAM,KAAK,CAAC,CAAC,EAAE,OAAO,CAAC;MAC3B,IAAIE,MAAM,KAAK,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC;MAE5B;MACA,OAAOF,MAAM,GAAGE,MAAM;IACxB,CAAC,CAAC;EACJ;EAEQlD,oBAAoBA,CAAC9C,KAAiB;IAC5C,OAAOA,KAAK,CAAC2F,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KACrBD,CAAC,CAACnE,IAAI,CAACwE,aAAa,CAACJ,CAAC,CAACpE,IAAI,EAAEyE,SAAS,EAAE;MAAEC,WAAW,EAAE;IAAM,CAAE,CAAC,CACjE;EACH;EACA;;;;;;;;;EASQV,sBAAsBA,CAC5BW,YAAsB,EACtBd,UAAoB;IAEpB;IACA,IAAIe,WAAW,GAAGf,UAAU,CAAChF,QAAQ,CAACgG,IAAI,CACvClG,IAAI,IACHA,IAAI,CAAC+B,GAAG,IACR,GACEiE,YAAY,CAAC1E,QAAQ,IAAI/C,gBAAgB,CAAC2D,KAAK,IAC/C8D,YAAY,CAAC1E,QAAQ,IAAI/C,gBAAgB,CAAC4D,gBAAgB,GACtD3D,WAAW,CAAC6E,YAAY,GACxB2C,YAAY,CAAC1E,QAAQ,KAAK/C,gBAAgB,CAAC8D,OAAO,GAClD7D,WAAW,CAACsF,cAAc,GAC1BtF,WAAW,CAACoF,kBAClB,IAAIoC,YAAY,CAAC1D,SAAS,EAAE,CAC/B;IAED,IAAI2D,WAAW,EAAE;MACf;MACAA,WAAW,CAAC/F,QAAQ,CAACD,IAAI,CAAC+F,YAAY,CAAC;MACvC,IAAI,CAACtD,oBAAoB,CAACuD,WAAW,CAAC/F,QAAQ,CAAC;KAChD,MAAM;MACL;MACA+F,WAAW,GAAG,IAAI,CAACE,oBAAoB,CAACH,YAAY,EAAEd,UAAU,CAACnD,GAAG,CAAC;MACrEmD,UAAU,EAAEhF,QAAQ,CAACD,IAAI,CAAC;QACxB,GAAGgG,WAAW;QACd/F,QAAQ,EAAE,IAAI,CAACwC,oBAAoB,CAAC,CAClC,GAAGuD,WAAW,CAAC/F,QAAQ,EACvB8F,YAAY,CACb;OACF,CAAC;MACF,IAAI,CAACV,aAAa,CAACJ,UAAU,CAAChF,QAAQ,CAAC;;EAE3C;EAEAkG,mBAAmBA,CAACC,QAAkB;IACpC,MAAMjF,QAAQ,GAAG,IAAI,CAAC/B,cAAc,CAAC0F,QAAQ,EAAE;IAC/C,MAAMG,UAAU,GAAG,IAAI,CAACC,aAAa,CAACkB,QAAQ,CAAC/D,SAAU,CAAC;IAC1D,IAAI4C,UAAU,EAAE;MAEZA,UAAU,CAACrC,IACZ,CAACyD,KAAK,CAACrG,IAAI,CACVoG,QAAQ,CAACxD,IAAyD,CACnE;MACDqC,UAAU,CAAChF,QAAQ,CAACD,IAAI,CAACoG,QAAQ,CAAC;MAClC,IAAI,CAAC3D,oBAAoB,CAACwC,UAAU,CAAChF,QAAQ,CAAC;MAC9C,IAAI,CAACb,cAAc,CAAC0B,IAAI,CAACK,QAAQ,CAAC;;EAEtC;EAEAmF,iBAAiBA,CAACnF,QAAkB;IAClC,MAAMoF,qBAAqB,GAAG,IAAI,CAACnH,cAAc,CAAC0F,QAAQ,EAAE;IAC5D,IAAIyB,qBAAqB,EAAE;MACzB,MAAMtB,UAAU,GAAG,IAAI,CAACuB,cAAc,CACpCrF,QAAQ,CAACW,GAAG,EACZyE,qBAAqB,CACtB;MACD,IAAItB,UAAU,EAAE;QACd,MAAMwB,YAAY,GAAGxB,UAAU,EAAEhF,QAAQ,CAACgG,IAAI,CAC3ClG,IAAI,IAAKA,IAAI,CAAC+B,GAAG,KAAKX,QAAQ,CAACW,GAAG,CACpC;QACD,IAAI2E,YAAY,IAAIA,YAAY,CAAC7D,IAAI,IAAIzB,QAAQ,CAACyB,IAAI,EAAE;UACtD6D,YAAY,CAACrF,IAAI,GAAGD,QAAQ,CAACyB,IAAI,CAACxB,IAAI;UACtC,IACEqF,YAAY,CAACpF,QAAQ,KAAK/C,gBAAgB,CAACmF,SAAS,IACpDgD,YAAY,CAACpF,QAAQ,KAAK/C,gBAAgB,CAACkF,SAAS,IACpDiD,YAAY,CAACpF,QAAQ,KAAK/C,gBAAgB,CAACgG,kBAAkB,EAC7D;YACA,IAAI,CAACoC,uBAAuB,CAC1BzB,UAAU,EACV9D,QAAQ,EACRoF,qBAAqB,CACtB;YACD,IACEE,YAAY,CAACpF,QAAQ,IAAI/C,gBAAgB,CAACmF,SAAS,IACnDgD,YAAY,CAACpF,QAAQ,KAAK/C,gBAAgB,CAACkF,SAAS,EACpD;cACA,MAAM4C,QAAQ,GAAGK,YAAY,CAAC7D,IAAgC;cAC9D;cACA;cACA;cACA,IAAI,CAAC+D,cAAc,CAACF,YAAY,CAAC7D,IAAI,EAAE;gBACrCxB,IAAI,EAAEgF,QAAQ,CAAChF,IAAI;gBACnBb,EAAE,EAAE6F,QAAQ,CAAC7F,EAAE;gBACfqG,WAAW,EAAER,QAAQ,CAACQ,WAAW;gBACjCC,QAAQ,EAAET,QAAQ,CAACS;eACpB,CAAC;aACH,MAAM;cACL,MAAMT,QAAQ,GAAGK,YAAY,CAAC7D,IAA8B;cAC5D,IAAI,CAAC+D,cAAc,CAACF,YAAY,CAAC7D,IAAI,EAAE;gBACrCxB,IAAI,EAAEgF,QAAQ,CAAChF,IAAI;gBACnBb,EAAE,EAAE6F,QAAQ,CAAC7F;eACd,CAAC;;WAEL,MAAM;YACL,MAAMuG,eAAe,GAAG3F,QAAQ,CAACyB,IAEH;YAC9B,IAAI,CAAC+D,cAAc,CAACF,YAAY,CAAC7D,IAAI,EAAE;cACrCxB,IAAI,EAAE0F,eAAe,CAAC1F,IAAI;cAC1Bb,EAAE,EAAEuG,eAAe,CAACvG,EAAE;cACtBwG,KAAK,EAAED,eAAe,CAACC,KAAK;cAC5BH,WAAW,EAAEE,eAAe,CAACF,WAAW;cACxC9E,GAAG,EAAEgF,eAAe,CAAChF,GAAG;cACxBkF,SAAS,EAAEF,eAAe,CAACE,SAAS;cACpCC,WAAW,EAAEH,eAAe,CAACG,WAAW;cACxCC,QAAQ,EAAEJ,eAAe,CAACI,QAAQ;cAClCC,IAAI,EAAEL,eAAe,CAACK;aACvB,CAAC;;UAEJ,IAAI,CAAC1E,oBAAoB,CAACwC,UAAU,EAAEhF,QAAQ,CAAC;UAC/C,IAAI,CAACoF,aAAa,CAACJ,UAAU,EAAEhF,QAAQ,CAAC;UACxC,IAAI,CAACb,cAAc,CAAC0B,IAAI,CAACyF,qBAAqB,CAAC;;;;EAIvD;EAEQI,cAAcA,CACpBF,YAAe,EACfW,YAAwB;IAExBC,MAAM,CAACC,IAAI,CAACF,YAAY,CAAC,CAACtH,OAAO,CAAEyH,GAAG,IAAI;MACxC,IAAIA,GAAG,IAAId,YAAY,EAAE;QACtBA,YAAoB,CAACc,GAAG,CAAC,GAAIH,YAAoB,CAACG,GAAG,CAAC;;IAE3D,CAAC,CAAC;EACJ;EAEAb,uBAAuBA,CACrBc,SAAmB,EACnBrG,QAAkB,EAClB/B,cAAwB;IAExB,IAAIoI,SAAS,CAAC5E,IAAI,IAAIzB,QAAQ,CAACyB,IAAI,EAAE;MAEjC4E,SAAS,CAAC5E,IACX,CAACyD,KAAK,CAACvG,OAAO,CAAE2H,IAAI,IAAI;QACvB,IAAIA,IAAI,CAAClH,EAAE,IAAIY,QAAQ,CAACyB,IAAI,EAAErC,EAAE,EAAE;UAChC8G,MAAM,CAACK,MAAM,CAACD,IAAI,EAAEtG,QAAQ,CAACyB,IAAI,CAAC;;MAEtC,CAAC,CAAC;MACF,IAAI,CAACxD,cAAc,CAAC0B,IAAI,CAAC1B,cAAc,CAAC;;EAE5C;EAEAuI,mBAAmBA,CAACxG,QAAkB;IACpC,MAAMoF,qBAAqB,GAAG,IAAI,CAACnH,cAAc,CAAC0F,QAAQ,EAAE;IAC5D,MAAMG,UAAU,GAAG,IAAI,CAACuB,cAAc,CACpCrF,QAAQ,CAACW,GAAG,EACZyE,qBAAsB,CACvB;IACD,IAAItB,UAAU,EAAE;MACd,MAAM2C,KAAK,GAAG3C,UAAU,EAAEhF,QAAQ,CAAC4H,SAAS,CACzC9H,IAAI,IAAKA,IAAI,CAAC+B,GAAG,KAAKX,QAAQ,CAACW,GAAG,CACpC;MACD,IACEX,QAAQ,CAACE,QAAQ,KAAK/C,gBAAgB,CAACmF,SAAS,IAChDtC,QAAQ,CAACE,QAAQ,KAAK/C,gBAAgB,CAACkF,SAAS,IAChDrC,QAAQ,CAACE,QAAQ,KAAK/C,gBAAgB,CAACgG,kBAAkB,EACzD;QAEEW,UAAU,CAACrC,IACZ,CAACyD,KAAK,GACLpB,UAAU,CAACrC,IACZ,CAACyD,KAAK,CAACyB,MAAM,CAAEL,IAAI,IAAKA,IAAI,CAAClH,EAAE,KAAKY,QAAQ,CAACyB,IAAI,EAAErC,EAAE,CAAC;;MAEzD,IAAIqH,KAAK,GAAG,CAAC,CAAC,EAAE;QACd3C,UAAU,EAAEhF,QAAQ,CAAC8H,MAAM,CAACH,KAAK,EAAE,CAAC,CAAC;QACrC,IACE3C,UAAU,CAAChF,QAAQ,CAACsB,MAAM,IAAI,CAAC,KAC9B0D,UAAU,CAAC5D,QAAQ,IAAI7C,oBAAoB,IAC1CyG,UAAU,CAAC5D,QAAQ,IAAI1C,mBAAmB,IAC1CsG,UAAU,CAAC5D,QAAQ,IAAI5C,sBAAsB,CAAC,EAChD;UACA,MAAMuJ,sBAAsB,GAAG,IAAI,CAACxB,cAAc,CAChDvB,UAAU,CAACnD,GAAG,EACdyE,qBAAsB,CACvB;UACD,IAAIyB,sBAAsB,EAAE;YAC1BA,sBAAsB,CAAC/H,QAAQ,GAC7B+H,sBAAsB,CAAC/H,QAAQ,CAAC6H,MAAM,CACnCG,KAAe,IAAKA,KAAK,CAACnG,GAAG,KAAKmD,UAAU,CAACnD,GAAG,CAClD;;;QAGP,IAAI,CAAC1C,cAAc,CAAC0B,IAAI,CAACyF,qBAAqB,CAAC;;;EAGrD;EAEA2B,QAAQA,CAACC,YAAsB,EAAEC,WAAqB;IACpD;IACA,MAAM7B,qBAAqB,GAAG,IAAI,CAACnH,cAAc,CAAC0F,QAAQ,EAAE;IAC5D;IACA,MAAMG,UAAU,GAAG,IAAI,CAACuB,cAAc,CACpC4B,WAAW,CAACtG,GAAG,EACfyE,qBAAsB,CACvB;IAED,IACE,CAAC,IAAI,CAAC8B,mBAAmB,CACvBF,YAAY,EACZC,WAAW,EACXnD,UAAU,EACVsB,qBAAsB,CACvB,EAED;IAEF,IAAItB,UAAU,EAAE;MACdA,UAAU,CAAChF,QAAQ,GAAGgF,UAAU,CAAChF,QAAQ,CAAC6H,MAAM,CAC7CG,KAAe,IAAKA,KAAK,CAACnG,GAAG,KAAKsG,WAAW,CAACtG,GAAG,CACnD;MACD,IACEmD,UAAU,CAAChF,QAAQ,CAACsB,MAAM,IAAI,CAAC,KAC9B0D,UAAU,CAAC5D,QAAQ,IAAI7C,oBAAoB,IAC1CyG,UAAU,CAAC5D,QAAQ,IAAI1C,mBAAmB,IAC1CsG,UAAU,CAAC5D,QAAQ,IAAI5C,sBAAsB,CAAC,EAChD;QACA,MAAMuJ,sBAAsB,GAAG,IAAI,CAACxB,cAAc,CAChDvB,UAAU,CAACnD,GAAG,EACdyE,qBAAsB,CACvB;QACD,IAAIyB,sBAAsB,EAAE;UAC1BA,sBAAsB,CAAC/H,QAAQ,GAC7B+H,sBAAsB,CAAC/H,QAAQ,CAAC6H,MAAM,CACnCG,KAAe,IAAKA,KAAK,CAACnG,GAAG,KAAKmD,UAAU,CAACnD,GAAG,CAClD;;;;IAIT;IAEA,IACEsG,WAAW,CAAC/G,QAAQ,KAAK/C,gBAAgB,CAAC6B,MAAM,IAChDgI,YAAY,CAAC9G,QAAQ,IAAI7C,oBAAoB,IAC7C2J,YAAY,CAAC9G,QAAQ,IAAI1C,mBAAmB,IAC5CwJ,YAAY,CAAC9G,QAAQ,IAAI5C,sBAAsB,EAC/C;MACA0J,YAAY,CAAClI,QAAQ,CAACD,IAAI,CAAC;QACzB,GAAGoI,WAAW;QACd/F,SAAS,EAAE8F,YAAY,CAACrG;OACzB,CAAC;MACF,IAAI,CAACW,oBAAoB,CAAC0F,YAAY,CAAClI,QAAQ,CAAC;MAChD,IAAI,CAACoF,aAAa,CAAC8C,YAAY,CAAClI,QAAQ,CAAC;MACzC,IAAI,CAACqI,gBAAgB,CAACH,YAAY,EAAEC,WAAW,CAAC;KACjD,MAAM;MACL,MAAMG,gBAAgB,GAAG,IAAI,CAACrD,aAAa,CAACiD,YAAY,CAACrG,GAAG,CAAC;MAC7D,IAAIyG,gBAAgB,EAAE;QACpB,MAAMvC,WAAW,GAAG,IAAI,CAACE,oBAAoB,CAC3CkC,WAAW,EACXG,gBAAgB,CAACzG,GAAG,CACrB;QACD,MAAM0G,mBAAmB,GAAGD,gBAAgB,EAAEtI,QAAQ,CAACgG,IAAI,CACxDlG,IAAc,IAAKA,IAAI,CAAC+B,GAAG,KAAKkE,WAAW,CAAClE,GAAG,CACjD;QACD,IAAI,CAACwG,gBAAgB,CAACC,gBAAgB,EAAEH,WAAW,CAAC;QACpD,IAAII,mBAAmB,EAAE;UACvBA,mBAAmB,CAACvI,QAAQ,CAACD,IAAI,CAAC;YAChC,GAAGoI,WAAW;YACd/F,SAAS,EAAEmG,mBAAmB,CAAC1G;WAChC,CAAC;UAEF,IAAI,CAACW,oBAAoB,CAAC+F,mBAAmB,CAACvI,QAAQ,CAAC;SACxD,MAAM;UACLsI,gBAAgB,CAACtI,QAAQ,CAACD,IAAI,CAAC;YAC7B,GAAGgG,WAAW;YACd/F,QAAQ,EAAE,CACR,GAAG+F,WAAW,CAAC/F,QAAQ,EACvB;cAAE,GAAGmI,WAAW;cAAE/F,SAAS,EAAE2D,WAAW,CAAClE;YAAG,CAAE,CAC/C;YACDO,SAAS,EAAE8F,YAAY,CAACrG;WACzB,CAAC;UACF,IAAI,CAACuD,aAAa,CAACkD,gBAAgB,CAACtI,QAAQ,CAAC;;;;IAInD;IACA,IAAI,CAACb,cAAc,CAAC0B,IAAI,CAACyF,qBAAqB,CAAC;EACjD;EAEQ8B,mBAAmBA,CACzBI,UAAoB,EACpBL,WAAqB,EACrBnD,UAA2B,EAC3B7F,cAAwB;IAExB,IACE6F,UAAU,EAAE5C,SAAS,KAAKjD,cAAc,EAAE0C,GAAG,IAC7C2G,UAAU,CAACpH,QAAQ,KAAK/C,gBAAgB,CAAC4C,OAAO,IAChDkH,WAAW,CAAC/G,QAAQ,KAAK/C,gBAAgB,CAAC6B,MAAM,EAChD;MACA,OAAO,KAAK;;IAEd,IACEiI,WAAW,CAACtG,GAAG,KAAK2G,UAAU,CAAC3G,GAAG,IAClCsG,WAAW,CAACtG,GAAG,IAAI2G,UAAU,CAACpG,SAAS,IACvC+F,WAAW,CAAC/F,SAAS,IAAIoG,UAAU,CAAC3G,GAAG,EAEvC,OAAO,KAAK;IAEd,IAAI2G,UAAU,CAACpH,QAAQ,KAAK/C,gBAAgB,CAAC8D,OAAO,EAAE,OAAO,KAAK;IAElE;IACA,IAAIsG,aAAa,GAAG,IAAI,CAAClC,cAAc,CAACiC,UAAU,CAAC3G,GAAG,EAAE1C,cAAc,CAAC;IACvE,IACE,CAACqJ,UAAU,CAACpH,QAAQ,IAAI7C,oBAAoB,IAC1CiK,UAAU,CAACpH,QAAQ,IAAI5C,sBAAsB,IAC7CgK,UAAU,CAACpH,QAAQ,IAAI1C,mBAAmB,KAC5C+J,aAAa,EAAErH,QAAQ,IAAI/C,gBAAgB,CAAC6B,MAAM,EAClD;MACA,OAAO,KAAK;;IAEd,OAAOuI,aAAa,EAAE;MACpB,IAAIA,aAAa,CAAC5G,GAAG,KAAKsG,WAAW,CAACtG,GAAG,EAAE;QACzC,OAAO,KAAK,CAAC,CAAC;;MAEhB4G,aAAa,GAAG,IAAI,CAAClC,cAAc,CAACkC,aAAa,CAAC5G,GAAG,EAAE1C,cAAc,CAAC;;IAExE,OAAO,IAAI;EACb;EAEAoH,cAAcA,CAACmC,OAAe,EAAEnG,MAAgB;IAC9C,IAAIA,MAAM,CAACvC,QAAQ,EAAE;MACnB,KAAK,IAAIgI,KAAK,IAAIzF,MAAM,CAACvC,QAAQ,EAAE;QACjC,IAAIgI,KAAK,CAACnG,GAAG,KAAK6G,OAAO,EAAE;UACzB,OAAOnG,MAAM;;QAEf,MAAMzC,IAAI,GAAG,IAAI,CAACyG,cAAc,CAACmC,OAAO,EAAEV,KAAK,CAAC;QAChD,IAAIlI,IAAI,EAAE,OAAOA,IAAI;;;IAGzB,OAAO,IAAI;EACb;EAEAmF,aAAaA,CAACpD,GAAW;IACvB,IAAIA,GAAG,IAAIvD,WAAW,CAAC2C,OAAO,EAAE;MAC9B,OAAO,IAAI,CAAC9B,cAAc,CAAC0F,QAAQ,EAAE;;IAEvC,OAAO,IAAI,CAAC/D,mBAAmB,EAAEkF,IAAI,CAAElG,IAAI,IAAKA,IAAI,CAAC+B,GAAG,IAAIA,GAAG,CAAC,IAAI,IAAI;EAC1E;EAEQoE,oBAAoBA,CAC1BkC,WAAqB,EACrB/F,SAAiB;IAEjB,MAAMuG,eAAe,GACnBR,WAAW,CAAC/G,QAAQ,KAAK/C,gBAAgB,CAAC2D,KAAK,IAC/CmG,WAAW,CAAC/G,QAAQ,KAAK/C,gBAAgB,CAAC4D,gBAAgB,GACtD,SAAS,GACTkG,WAAW,CAAC/G,QAAQ,KAAK/C,gBAAgB,CAAC8D,OAAO,GACjD,UAAU,GACV,cAAc;IACpB,MAAMyG,cAAc,GAClBT,WAAW,CAAC/G,QAAQ,KAAK/C,gBAAgB,CAAC2D,KAAK,IAC/CmG,WAAW,CAAC/G,QAAQ,KAAK/C,gBAAgB,CAAC4D,gBAAgB,GACtD3D,WAAW,CAAC6E,YAAY,GACxBgF,WAAW,CAAC/G,QAAQ,KAAK/C,gBAAgB,CAAC8D,OAAO,GACjD7D,WAAW,CAACsF,cAAc,GAC1BtF,WAAW,CAACoF,kBAAkB;IACpC,MAAMmF,mBAAmB,GACvBV,WAAW,CAAC/G,QAAQ,KAAK/C,gBAAgB,CAAC2D,KAAK,IAC/CmG,WAAW,CAAC/G,QAAQ,KAAK/C,gBAAgB,CAAC4D,gBAAgB,GACtD1D,oBAAoB,GACpB4J,WAAW,CAAC/G,QAAQ,KAAK/C,gBAAgB,CAAC8D,OAAO,GACjD3D,sBAAsB,GACtBE,mBAAmB;IACzB,MAAMoK,eAAe,GACnBX,WAAW,CAAC/G,QAAQ,KAAK/C,gBAAgB,CAAC2D,KAAK,IAC/CmG,WAAW,CAAC/G,QAAQ,KAAK/C,gBAAgB,CAAC4D,gBAAgB,GACtD7D,YAAY,CAAC4D,KAAK,GAClBmG,WAAW,CAAC/G,QAAQ,KAAK/C,gBAAgB,CAAC8D,OAAO,GACjD/D,YAAY,CAAC+D,OAAO,GACpB/D,YAAY,CAAC8D,WAAW;IAC9B,OAAO;MACLf,IAAI,EAAEwH,eAAe;MACrB3I,QAAQ,EAAE,EAAE;MACZoB,QAAQ,EAAEyH,mBAAmB;MAC7BhH,GAAG,EAAE,GAAG+G,cAAc,IAAIxG,SAAS,EAAE;MACrCA,SAAS,EAAEA,SAAS;MACpBN,IAAI,EAAEgH,eAAe;MACrB/G,eAAe,EAAE,CAACoG,WAAW,CAAC/G,QAAQ;KACvC;EACH;EAEQiH,gBAAgBA,CAACG,UAAoB,EAAEL,WAAqB;IAClE,IAAIA,WAAW,CAACxF,IAAI,EAAE;MACpB,IAAI6F,UAAU,CAACpH,QAAQ,KAAK/C,gBAAgB,CAAC6B,MAAM,EAAE;QACnD,IAAIiI,WAAW,CAAC/G,QAAQ,KAAK/C,gBAAgB,CAAC2D,KAAK,EAAE;UACnD,IAAI,CAAClD,aAAa,CACfiK,qBAAqB,CAAC;YACrBzI,EAAE,EAAE,CAAC6H,WAAW,EAAExF,IAA6B,EAAC+B,eAAe;YAC/DsE,QAAQ,EAAE,CAACR,UAAU,EAAE7F,IAAuB,EAACqG;WAChD,CAAC,CACD5I,SAAS,EAAE;SACf,MAAM,IAAI+H,WAAW,CAAC/G,QAAQ,KAAK/C,gBAAgB,CAAC6D,WAAW,EAAE;UAChE,IAAI,CAACnD,mBAAmB,CACrBkK,oBAAoB,CAAC;YACpB3I,EAAE,EAAE,CAAC6H,WAAW,EAAExF,IAAmC,EAClD4B,qBAAqB;YACxByE,QAAQ,EAAE,CAACR,UAAU,EAAE7F,IAAuB,EAACqG;WAChD,CAAC,CACD5I,SAAS,EAAE;SACf,MAAM,IAAI+H,WAAW,CAAC/G,QAAQ,KAAK/C,gBAAgB,CAAC8D,OAAO,EAAE;UAC5D,IAAI,CAACnD,cAAc,CAACkK,mBAAmB,CAAC;YACtC5I,EAAE,EAAE,CAAC6H,WAAW,EAAExF,IAAgB,EAACrC,EAAG;YACtC0I,QAAQ,EAAE,CAACR,UAAU,EAAE7F,IAAuB,EAACqG;WAChD,CAAC;SACH,MAAM,IAAIb,WAAW,CAAC/G,QAAQ,KAAK/C,gBAAgB,CAAC6B,MAAM,EAAE;UAC3D,IAAI,CAAClB,cAAc,CAACmK,kBAAkB,CAAC;YACrC7I,EAAE,EAAE,CAAC6H,WAAW,EAAExF,IAAuB,EAACqG,QAAS;YACnDI,cAAc,EAAE,CAACZ,UAAU,EAAE7F,IAAuB,EAACqG;WACtD,CAAC;;OAEL,MAAM;QACL,IAAIb,WAAW,CAAC/G,QAAQ,KAAK/C,gBAAgB,CAAC2D,KAAK,EAAE;UACnD,IAAI,CAAClD,aAAa,CACfuK,yBAAyB,CACxB,CAAClB,WAAW,EAAExF,IAA6B,EAAC+B,eAAe,CAC5D,CACAtE,SAAS,EAAE;SACf,MAAM,IAAI+H,WAAW,CAAC/G,QAAQ,KAAK/C,gBAAgB,CAAC6D,WAAW,EAAE;UAChE,IAAI,CAACnD,mBAAmB,CACrBuK,wBAAwB,CACvB,CAACnB,WAAW,EAAExF,IAAmC,EAC9C4B,qBAAqB,CACzB,CACAnE,SAAS,EAAE;SACf,MAAM,IAAI+H,WAAW,CAAC/G,QAAQ,KAAK/C,gBAAgB,CAAC8D,OAAO,EAAE;UAC5D,IAAI,CAACnD,cAAc,CAACuK,uBAAuB,CACzC,CAACpB,WAAW,EAAExF,IAAgB,EAACrC,EAAG,CACnC;SACF,MAAM,IAAI6H,WAAW,CAAC/G,QAAQ,KAAK/C,gBAAgB,CAAC6B,MAAM,EAAE;UAC3D,IAAI,CAAClB,cAAc,CAACwK,sBAAsB,CACxC,CAACrB,WAAW,EAAExF,IAAuB,EAACqG,QAAS,CAChD;;;;EAIT;EAEAS,yBAAyBA,CAACC,UAAoB;IAC5C,MAAMhK,KAAK,GAAe,EAAE;IAC5B;IACA,MAAMiK,iBAAiB,GAAI7J,IAAc,IAAI;MAC3CA,IAAI,CAACE,QAAQ,CAACH,OAAO,CAAEmI,KAAK,IAAI;QAC9B,IACEA,KAAK,CAAC5G,QAAQ,KAAK7C,oBAAoB,IACvCyJ,KAAK,CAAC5G,QAAQ,KAAK1C,mBAAmB,EACtC;UACAgB,KAAK,CAACK,IAAI,CAAC,GAAGiI,KAAK,CAAChI,QAAQ,CAAC;SAC9B,MAAM,IAAIgI,KAAK,CAAC5G,QAAQ,KAAK/C,gBAAgB,CAAC6B,MAAM,EAAE;UACrDyJ,iBAAiB,CAAC3B,KAAK,CAAC;;MAE5B,CAAC,CAAC;IACJ,CAAC;IACD;IACA2B,iBAAiB,CAACD,UAAU,CAAC;IAC7B,OAAOhK,KAAK;EACd;EAEA;;;;;;EAMAkK,aAAaA,CAAC/H,GAAW;IACvB,MAAM/B,IAAI,GAAG,IAAI,CAACmF,aAAa,CAACpD,GAAG,CAAC;IACpC,IAAI/B,IAAI,EAAE,IAAI,CAAC4H,mBAAmB,CAAC5H,IAAI,CAAC;EAC1C;EAEA+J,cAAcA,CAACzH,SAAiB,EAAE1C,KAAiB;IACjD,MAAMP,cAAc,GAAG,IAAI,CAACA,cAAc,CAAC0F,QAAQ,EAAE;IACrD,IAAI1F,cAAc,EAAE;MAClB,MAAM6F,UAAU,GAAG,IAAI,CAACuB,cAAc,CAACnE,SAAS,EAAEjD,cAAc,CAAC;MACjE,IAAI6F,UAAU,EAAE;QACd,IAAIA,UAAU,CAAC5D,QAAQ,IAAI/C,gBAAgB,CAAC6B,MAAM,EAAE;UAClD,IAAIR,KAAK,CAACoK,IAAI,CAAEhK,IAAI,IAAKA,IAAI,CAAC+B,GAAG,KAAKmD,UAAU,CAACnD,GAAG,CAAC,EAAE,OAAO,IAAI,CAAC,KAC9D,OAAO,IAAI,CAACgI,cAAc,CAAC7E,UAAU,CAACnD,GAAG,EAAEnC,KAAK,CAAC;;QAExD,OAAOA,KAAK,CAACoK,IAAI,CAAEhK,IAAI,IAAKA,IAAI,CAAC+B,GAAG,KAAKmD,UAAU,CAACnD,GAAG,CAAC;OACzD,MAAM,OAAO,KAAK;KACpB,MAAM,OAAO,KAAK;EACrB;EAEAkI,4BAA4BA,CAAA;IAC1B,MAAMC,iBAAiB,GAAG,QAAQ3L,gBAAgB,CAAC8D,OAAO,IAAI,IAAI,CAAC5C,gBAAgB,EAAE;IACrF,MAAMJ,cAAc,GAAG,IAAI,CAACA,cAAc,CAAC0F,QAAQ,EAAE;IACrD,IAAI1F,cAAc,EAAE;MAClB,MAAM4G,WAAW,GAAG,IAAI,CAACQ,cAAc,CACrCyD,iBAAiB,EACjB7K,cAAc,CACf;MACD,IAAI4G,WAAW,EAAE;QACf,OAAO,IAAI,CAACd,aAAa,CAACc,WAAW,CAAC3D,SAAU,CAAC;OAClD,MAAM,OAAO,IAAI;KACnB,MAAM,OAAO,IAAI;EACpB;EAAC,QAAA6H,CAAA,G;qBAr4BUtL,eAAe,EAAAuL,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,iBAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,YAAA,GAAAL,EAAA,CAAAC,QAAA,CAAAK,EAAA,CAAAC,kBAAA,GAAAP,EAAA,CAAAC,QAAA,CAAAO,EAAA,CAAAC,aAAA,GAAAT,EAAA,CAAAC,QAAA,CAAAS,EAAA,CAAAC,YAAA;EAAA;EAAA,QAAAC,EAAA,G;WAAfnM,eAAe;IAAAoM,OAAA,EAAfpM,eAAe,CAAAqM,IAAA;IAAAC,UAAA,EAFd;EAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}