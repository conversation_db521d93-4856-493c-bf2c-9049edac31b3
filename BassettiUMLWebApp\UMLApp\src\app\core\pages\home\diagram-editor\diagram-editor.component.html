<mat-drawer-container class="project-container">
  <mat-drawer mode="side" opened class="side-container" [disableClose]="true">
    <mat-accordion multi="true">
      <mat-expansion-panel
        class="expansion-panel library-panel"
        [hideToggle]="true"
        expanded="true"
        disabled="true"
      >
        <mat-expansion-panel-header class="disable_ripple">
          <mat-panel-title class="panel-title">
            {{ "diagram.library" | translate }}
          </mat-panel-title>
          <button
            [disabled]="!hasEditAccessOnly"
            mat-icon-button
            (click)="onCreateFolder($event)"
            matRippleDisabled
          >
            <mat-icon>create_new_folder</mat-icon>
          </button>
        </mat-expansion-panel-header>
        <div class="tree-container">
          <app-library-tree
            [expandNodeTag]="selectedNodeTag"
          ></app-library-tree>
        </div>
      </mat-expansion-panel>

      <!-- Hide the components panel when version history is shown -->
      <mat-expansion-panel
        class="expansion-panel"
        [hideToggle]="true"
        expanded="true"
        disabled="true"
        [style.visibility]="showVersionHistory ? 'hidden' : 'visible'"
        [style.height]="showVersionHistory ? '0' : 'auto'"
      >
        <mat-expansion-panel-header class="disable_ripple">
          <mat-panel-title class="panel-title">
            {{ "diagram.components" | translate }}
          </mat-panel-title>
        </mat-expansion-panel-header>
        <div id="classDiagram" class="disable_ripple component-panel"></div>
      </mat-expansion-panel>
    </mat-accordion>
    <div class="property" *ngIf="hasEditAccessOnly">
      <app-properties
        style="margin-top: 20px; padding-top: 20px"
        [nodeData]="propertyData"
        (onChangePropertyData)="
          onUpdateProperties($event, libraryTreeComponent.selectedTreeNode())
        "
        [editAccess]="hasEditAccessOnly"
        (isColorPickerSelected)="onColorPickerSelection($event)"
        [dataTypes]="attributeTypes"
        [idDiagram]="selectedDiagramId"
      ></app-properties>
    </div>
  </mat-drawer>
  <mat-drawer-content [ngClass]="{ 'content-margin': showVersionHistory }">
    <div class="main-container" id="diagramDiv"></div>
    <app-action-controls [diagram]="diagram"></app-action-controls>
  </mat-drawer-content>
</mat-drawer-container>

<!-- Overlay and Version History Drawer -->
<mat-drawer-container
  class="version-history-container"
  *ngIf="showVersionHistory"
>
  <mat-drawer mode="over" opened class="version-history-drawer">
    <app-version-history></app-version-history>
  </mat-drawer>
</mat-drawer-container>
