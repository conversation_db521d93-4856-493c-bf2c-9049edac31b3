{"ast": null, "code": "import { BehaviorSubject } from 'rxjs';\nimport { GojsNodeCategory } from 'src/app/shared/model/gojs';\nimport * as i0 from \"@angular/core\";\nexport class PropertyService {\n  constructor() {\n    // Convert to signals\n    this._propertyData = signal(null);\n    this.propertyData = this._propertyData.asReadonly();\n    // Keep BehaviorSubject for backward compatibility during transition\n    this._propertyDataSubject = new BehaviorSubject(null);\n  }\n  setPropertyData(propertyData) {\n    this._propertyData.set(propertyData);\n    this._propertyDataSubject.next(propertyData);\n  }\n  propertyDataChanges() {\n    return this._propertyDataSubject.asObservable();\n  }\n  transferDataOnSelection(node) {\n    const nodeData = node.data;\n    if (node.isSelected) {\n      if (nodeData.category == GojsNodeCategory.Class || nodeData.category == GojsNodeCategory.AssociativeClass) {\n        this.setPropertyData({\n          id: nodeData.id,\n          key: nodeData.key,\n          color: nodeData.color,\n          icon: nodeData.icon,\n          name: nodeData.name,\n          category: nodeData.category,\n          size: nodeData.size,\n          isGroup: nodeData.isGroup,\n          supportingLevels: nodeData.supportingLevels,\n          allowTopLevelDrops: nodeData.allowTopLevelDrops,\n          editable: nodeData.editable,\n          idTemplateClass: nodeData.idTemplateClass,\n          items: nodeData.items,\n          position: nodeData.position,\n          showTablePanel: nodeData.showTablePanel,\n          description: nodeData.description,\n          tag: nodeData.tag,\n          volumetry: nodeData.volumetry,\n          treeNodeTag: nodeData.treeNodeTag\n        });\n      } else if (nodeData.category == GojsNodeCategory.Enumeration) {\n        this.setPropertyData({\n          id: nodeData.id,\n          key: nodeData.key,\n          color: nodeData.color,\n          icon: nodeData.icon,\n          name: nodeData.name,\n          category: nodeData.category,\n          size: nodeData.size,\n          isGroup: nodeData.isGroup,\n          supportingLevels: nodeData.supportingLevels,\n          allowTopLevelDrops: nodeData.allowTopLevelDrops,\n          editable: nodeData.editable,\n          idTemplateEnumeration: nodeData.idTemplateEnumeration,\n          items: nodeData.items,\n          position: nodeData.position,\n          showTablePanel: nodeData.showTablePanel,\n          description: nodeData.description,\n          tag: nodeData.tag,\n          volumetry: nodeData.volumetry,\n          treeNodeTag: nodeData.treeNodeTag\n        });\n      } else if (nodeData.category == GojsNodeCategory.Attribute || nodeData.category == GojsNodeCategory.Operation) {\n        this.setPropertyData({\n          id: nodeData.id,\n          key: nodeData.key,\n          icon: nodeData.icon,\n          name: nodeData.name,\n          description: nodeData?.description,\n          category: nodeData.category,\n          isGroup: nodeData.isGroup,\n          allowTopLevelDrops: nodeData.allowTopLevelDrops,\n          editable: nodeData.editable,\n          showTablePanel: nodeData.showTablePanel,\n          dataType: nodeData.dataType,\n          isAttribute: nodeData.isAttribute,\n          memberType: nodeData.memberType,\n          idClass: nodeData.idClass,\n          treeNodeTag: nodeData.treeNodeTag\n        });\n      } else if (nodeData.category == GojsNodeCategory.EnumerationLiteral) {\n        this.setPropertyData({\n          id: nodeData.id,\n          key: nodeData.key,\n          icon: nodeData.icon,\n          name: nodeData.name,\n          category: nodeData.category,\n          isGroup: nodeData.isGroup,\n          allowTopLevelDrops: nodeData.allowTopLevelDrops,\n          editable: nodeData.editable,\n          showTablePanel: nodeData.showTablePanel,\n          dataType: nodeData.dataType,\n          isAttribute: nodeData.isAttribute,\n          idEnumeration: nodeData.idEnumeration,\n          treeNodeTag: nodeData.treeNodeTag\n        });\n      } else if (nodeData.category == GojsNodeCategory.Folder) {\n        this.setPropertyData({\n          id: nodeData.id,\n          key: nodeData.key,\n          icon: nodeData.icon,\n          name: nodeData.name,\n          category: nodeData.category,\n          isGroup: nodeData.isGroup,\n          allowTopLevelDrops: nodeData.allowTopLevelDrops,\n          editable: nodeData.editable,\n          showTablePanel: nodeData.showTablePanel,\n          parent: nodeData.parent,\n          idFolder: nodeData.idFolder,\n          isFolder: nodeData.isFolder\n        });\n      } else if (nodeData.category = GojsNodeCategory.Association) {\n        this.setPropertyData({\n          cardinalityFrom: nodeData.cardinalityFrom,\n          cardinalityTo: nodeData.cardinalityTo,\n          category: nodeData.category,\n          color: nodeData.color,\n          editable: nodeData.editable,\n          from: nodeData.from,\n          fromPort: nodeData.fromPort,\n          id: nodeData.id,\n          idDestinationTempClass: nodeData.idDestinationTempClass,\n          idFromClass: nodeData.idFromClass,\n          idLinkType: nodeData.idLinkType,\n          idSourceTempClass: nodeData.idSourceTempClass,\n          idToClass: nodeData.idToClass,\n          key: nodeData.key,\n          name: nodeData.name,\n          to: nodeData.to,\n          toPort: nodeData.toPort,\n          fromComment: nodeData.fromComment,\n          toComment: nodeData.toComment,\n          segmentOffset: nodeData.segmentOffset\n        });\n      }\n    } else {\n      this.setPropertyData(null);\n    }\n  }\n  static #_ = this.ɵfac = function PropertyService_Factory(t) {\n    return new (t || PropertyService)();\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: PropertyService,\n    factory: PropertyService.ɵfac,\n    providedIn: 'root'\n  });\n}", "map": {"version": 3, "names": ["BehaviorSubject", "GojsNodeCategory", "PropertyService", "constructor", "_propertyData", "signal", "propertyData", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_propertyDataSubject", "setPropertyData", "set", "next", "propertyDataChanges", "asObservable", "transferDataOnSelection", "node", "nodeData", "data", "isSelected", "category", "Class", "AssociativeClass", "id", "key", "color", "icon", "name", "size", "isGroup", "supportingLevels", "allowTopLevelDrops", "editable", "idTemplateClass", "items", "position", "showTablePanel", "description", "tag", "volumetry", "treeNodeTag", "Enumeration", "idTemplateEnumeration", "Attribute", "Operation", "dataType", "isAttribute", "memberType", "idClass", "EnumerationLiteral", "idEnumeration", "Folder", "parent", "idFolder", "isFolder", "Association", "cardinalityFrom", "cardinalityTo", "from", "fromPort", "idDestinationTempClass", "idFromClass", "idLinkType", "idSourceTempClass", "idToClass", "to", "to<PERSON><PERSON>", "fromComment", "toComment", "segmentOffset", "_", "_2", "factory", "ɵfac", "providedIn"], "sources": ["D:\\GitHub\\Bassetti\\devint-BASSETTI-GROUP-APP\\BassettiUMLWebApp\\UMLApp\\src\\app\\core\\services\\property\\property.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport * as go from 'gojs';\r\nimport { BehaviorSubject, Observable } from 'rxjs';\r\n\r\nimport {\r\n  GojsDiagramAttributeNode,\r\n  GojsDiagramClassNode,\r\n  GojsDiagramEnumerationNode,\r\n  GojsDiagramLiteralNode,\r\n  GojsFolderNode,\r\n  GojsLinkNode,\r\n  GojsNodeCategory,\r\n} from 'src/app/shared/model/gojs';\r\n\r\n@Injectable({\r\n  providedIn: 'root',\r\n})\r\nexport class PropertyService {\r\n  // Convert to signals\r\n  private _propertyData = signal<\r\n    | GojsDiagramClassNode\r\n    | GojsDiagramEnumerationNode\r\n    | GojsDiagramAttributeNode\r\n    | GojsDiagramLiteralNode\r\n    | GojsFolderNode\r\n    | GojsLinkNode\r\n    | null\r\n  >(null);\r\n\r\n  public readonly propertyData = this._propertyData.asReadonly();\r\n\r\n  // Keep BehaviorSubject for backward compatibility during transition\r\n  private _propertyDataSubject = new BehaviorSubject<\r\n    | GojsDiagramClassNode\r\n    | GojsDiagramEnumerationNode\r\n    | GojsDiagramAttributeNode\r\n    | GojsDiagramLiteralNode\r\n    | GojsFolderNode\r\n    | GojsLinkNode\r\n    | null\r\n  >(null);\r\n\r\n  constructor() {}\r\n\r\n  setPropertyData(\r\n    propertyData:\r\n      | GojsDiagramClassNode\r\n      | GojsDiagramEnumerationNode\r\n      | GojsDiagramAttributeNode\r\n      | GojsDiagramLiteralNode\r\n      | GojsFolderNode\r\n      | GojsLinkNode\r\n      | null\r\n  ): void {\r\n    this._propertyData.set(propertyData);\r\n    this._propertyDataSubject.next(propertyData);\r\n  }\r\n\r\n  propertyDataChanges(): Observable<\r\n    | GojsDiagramClassNode\r\n    | GojsDiagramEnumerationNode\r\n    | GojsDiagramAttributeNode\r\n    | GojsDiagramLiteralNode\r\n    | GojsFolderNode\r\n    | GojsLinkNode\r\n    | null\r\n  > {\r\n    return this._propertyDataSubject.asObservable();\r\n  }\r\n\r\n  transferDataOnSelection(node: go.Part) {\r\n    const nodeData = node.data;\r\n    if (node.isSelected) {\r\n      if (\r\n        nodeData.category == GojsNodeCategory.Class ||\r\n        nodeData.category == GojsNodeCategory.AssociativeClass\r\n      ) {\r\n        this.setPropertyData({\r\n          id: nodeData.id,\r\n          key: nodeData.key,\r\n          color: nodeData.color,\r\n          icon: nodeData.icon,\r\n          name: nodeData.name,\r\n          category: nodeData.category,\r\n          size: nodeData.size,\r\n          isGroup: nodeData.isGroup,\r\n          supportingLevels: nodeData.supportingLevels,\r\n          allowTopLevelDrops: nodeData.allowTopLevelDrops,\r\n          editable: nodeData.editable,\r\n          idTemplateClass: nodeData.idTemplateClass,\r\n          items: nodeData.items,\r\n          position: nodeData.position,\r\n          showTablePanel: nodeData.showTablePanel,\r\n          description: nodeData.description,\r\n          tag: nodeData.tag,\r\n          volumetry: nodeData.volumetry,\r\n          treeNodeTag: nodeData.treeNodeTag,\r\n        } as GojsDiagramClassNode);\r\n      } else if (nodeData.category == GojsNodeCategory.Enumeration) {\r\n        this.setPropertyData({\r\n          id: nodeData.id,\r\n          key: nodeData.key,\r\n          color: nodeData.color,\r\n          icon: nodeData.icon,\r\n          name: nodeData.name,\r\n          category: nodeData.category,\r\n          size: nodeData.size,\r\n          isGroup: nodeData.isGroup,\r\n          supportingLevels: nodeData.supportingLevels,\r\n          allowTopLevelDrops: nodeData.allowTopLevelDrops,\r\n          editable: nodeData.editable,\r\n          idTemplateEnumeration: nodeData.idTemplateEnumeration,\r\n          items: nodeData.items,\r\n          position: nodeData.position,\r\n          showTablePanel: nodeData.showTablePanel,\r\n          description: nodeData.description,\r\n          tag: nodeData.tag,\r\n          volumetry: nodeData.volumetry,\r\n          treeNodeTag: nodeData.treeNodeTag,\r\n        } as GojsDiagramEnumerationNode);\r\n      } else if (\r\n        nodeData.category == GojsNodeCategory.Attribute ||\r\n        nodeData.category == GojsNodeCategory.Operation\r\n      ) {\r\n        this.setPropertyData({\r\n          id: nodeData.id,\r\n          key: nodeData.key,\r\n          icon: nodeData.icon,\r\n          name: nodeData.name,\r\n          description: nodeData?.description,\r\n          category: nodeData.category,\r\n          isGroup: nodeData.isGroup,\r\n          allowTopLevelDrops: nodeData.allowTopLevelDrops,\r\n          editable: nodeData.editable,\r\n          showTablePanel: nodeData.showTablePanel,\r\n          dataType: nodeData.dataType,\r\n          isAttribute: nodeData.isAttribute,\r\n          memberType: nodeData.memberType,\r\n          idClass: nodeData.idClass,\r\n          treeNodeTag: nodeData.treeNodeTag,\r\n        } as GojsDiagramAttributeNode);\r\n      } else if (nodeData.category == GojsNodeCategory.EnumerationLiteral) {\r\n        this.setPropertyData({\r\n          id: nodeData.id,\r\n          key: nodeData.key,\r\n          icon: nodeData.icon,\r\n          name: nodeData.name,\r\n          category: nodeData.category,\r\n          isGroup: nodeData.isGroup,\r\n          allowTopLevelDrops: nodeData.allowTopLevelDrops,\r\n          editable: nodeData.editable,\r\n          showTablePanel: nodeData.showTablePanel,\r\n          dataType: nodeData.dataType,\r\n          isAttribute: nodeData.isAttribute,\r\n          idEnumeration: nodeData.idEnumeration,\r\n          treeNodeTag: nodeData.treeNodeTag,\r\n        } as GojsDiagramLiteralNode);\r\n      } else if (nodeData.category == GojsNodeCategory.Folder) {\r\n        this.setPropertyData({\r\n          id: nodeData.id,\r\n          key: nodeData.key,\r\n          icon: nodeData.icon,\r\n          name: nodeData.name,\r\n          category: nodeData.category,\r\n          isGroup: nodeData.isGroup,\r\n          allowTopLevelDrops: nodeData.allowTopLevelDrops,\r\n          editable: nodeData.editable,\r\n          showTablePanel: nodeData.showTablePanel,\r\n          parent: nodeData.parent,\r\n          idFolder: nodeData.idFolder,\r\n          isFolder: nodeData.isFolder,\r\n        } as GojsFolderNode);\r\n      } else if ((nodeData.category = GojsNodeCategory.Association)) {\r\n        this.setPropertyData({\r\n          cardinalityFrom: nodeData.cardinalityFrom,\r\n          cardinalityTo: nodeData.cardinalityTo,\r\n          category: nodeData.category,\r\n          color: nodeData.color,\r\n          editable: nodeData.editable,\r\n          from: nodeData.from,\r\n          fromPort: nodeData.fromPort,\r\n          id: nodeData.id,\r\n          idDestinationTempClass: nodeData.idDestinationTempClass,\r\n          idFromClass: nodeData.idFromClass,\r\n          idLinkType: nodeData.idLinkType,\r\n          idSourceTempClass: nodeData.idSourceTempClass,\r\n          idToClass: nodeData.idToClass,\r\n          key: nodeData.key,\r\n          name: nodeData.name,\r\n          to: nodeData.to,\r\n          toPort: nodeData.toPort,\r\n          fromComment: nodeData.fromComment,\r\n          toComment: nodeData.toComment,\r\n          segmentOffset: nodeData.segmentOffset,\r\n        } as GojsLinkNode);\r\n      }\r\n    } else {\r\n      this.setPropertyData(null);\r\n    }\r\n  }\r\n}\r\n"], "mappings": "AAEA,SAASA,eAAe,QAAoB,MAAM;AAElD,SAOEC,gBAAgB,QACX,2BAA2B;;AAKlC,OAAM,MAAOC,eAAe;EAyB1BC,YAAA;IAxBA;IACQ,KAAAC,aAAa,GAAGC,MAAM,CAQ5B,IAAI,CAAC;IAES,KAAAC,YAAY,GAAG,IAAI,CAACF,aAAa,CAACG,UAAU,EAAE;IAE9D;IACQ,KAAAC,oBAAoB,GAAG,IAAIR,eAAe,CAQhD,IAAI,CAAC;EAEQ;EAEfS,eAAeA,CACbH,YAOQ;IAER,IAAI,CAACF,aAAa,CAACM,GAAG,CAACJ,YAAY,CAAC;IACpC,IAAI,CAACE,oBAAoB,CAACG,IAAI,CAACL,YAAY,CAAC;EAC9C;EAEAM,mBAAmBA,CAAA;IASjB,OAAO,IAAI,CAACJ,oBAAoB,CAACK,YAAY,EAAE;EACjD;EAEAC,uBAAuBA,CAACC,IAAa;IACnC,MAAMC,QAAQ,GAAGD,IAAI,CAACE,IAAI;IAC1B,IAAIF,IAAI,CAACG,UAAU,EAAE;MACnB,IACEF,QAAQ,CAACG,QAAQ,IAAIlB,gBAAgB,CAACmB,KAAK,IAC3CJ,QAAQ,CAACG,QAAQ,IAAIlB,gBAAgB,CAACoB,gBAAgB,EACtD;QACA,IAAI,CAACZ,eAAe,CAAC;UACnBa,EAAE,EAAEN,QAAQ,CAACM,EAAE;UACfC,GAAG,EAAEP,QAAQ,CAACO,GAAG;UACjBC,KAAK,EAAER,QAAQ,CAACQ,KAAK;UACrBC,IAAI,EAAET,QAAQ,CAACS,IAAI;UACnBC,IAAI,EAAEV,QAAQ,CAACU,IAAI;UACnBP,QAAQ,EAAEH,QAAQ,CAACG,QAAQ;UAC3BQ,IAAI,EAAEX,QAAQ,CAACW,IAAI;UACnBC,OAAO,EAAEZ,QAAQ,CAACY,OAAO;UACzBC,gBAAgB,EAAEb,QAAQ,CAACa,gBAAgB;UAC3CC,kBAAkB,EAAEd,QAAQ,CAACc,kBAAkB;UAC/CC,QAAQ,EAAEf,QAAQ,CAACe,QAAQ;UAC3BC,eAAe,EAAEhB,QAAQ,CAACgB,eAAe;UACzCC,KAAK,EAAEjB,QAAQ,CAACiB,KAAK;UACrBC,QAAQ,EAAElB,QAAQ,CAACkB,QAAQ;UAC3BC,cAAc,EAAEnB,QAAQ,CAACmB,cAAc;UACvCC,WAAW,EAAEpB,QAAQ,CAACoB,WAAW;UACjCC,GAAG,EAAErB,QAAQ,CAACqB,GAAG;UACjBC,SAAS,EAAEtB,QAAQ,CAACsB,SAAS;UAC7BC,WAAW,EAAEvB,QAAQ,CAACuB;SACC,CAAC;OAC3B,MAAM,IAAIvB,QAAQ,CAACG,QAAQ,IAAIlB,gBAAgB,CAACuC,WAAW,EAAE;QAC5D,IAAI,CAAC/B,eAAe,CAAC;UACnBa,EAAE,EAAEN,QAAQ,CAACM,EAAE;UACfC,GAAG,EAAEP,QAAQ,CAACO,GAAG;UACjBC,KAAK,EAAER,QAAQ,CAACQ,KAAK;UACrBC,IAAI,EAAET,QAAQ,CAACS,IAAI;UACnBC,IAAI,EAAEV,QAAQ,CAACU,IAAI;UACnBP,QAAQ,EAAEH,QAAQ,CAACG,QAAQ;UAC3BQ,IAAI,EAAEX,QAAQ,CAACW,IAAI;UACnBC,OAAO,EAAEZ,QAAQ,CAACY,OAAO;UACzBC,gBAAgB,EAAEb,QAAQ,CAACa,gBAAgB;UAC3CC,kBAAkB,EAAEd,QAAQ,CAACc,kBAAkB;UAC/CC,QAAQ,EAAEf,QAAQ,CAACe,QAAQ;UAC3BU,qBAAqB,EAAEzB,QAAQ,CAACyB,qBAAqB;UACrDR,KAAK,EAAEjB,QAAQ,CAACiB,KAAK;UACrBC,QAAQ,EAAElB,QAAQ,CAACkB,QAAQ;UAC3BC,cAAc,EAAEnB,QAAQ,CAACmB,cAAc;UACvCC,WAAW,EAAEpB,QAAQ,CAACoB,WAAW;UACjCC,GAAG,EAAErB,QAAQ,CAACqB,GAAG;UACjBC,SAAS,EAAEtB,QAAQ,CAACsB,SAAS;UAC7BC,WAAW,EAAEvB,QAAQ,CAACuB;SACO,CAAC;OACjC,MAAM,IACLvB,QAAQ,CAACG,QAAQ,IAAIlB,gBAAgB,CAACyC,SAAS,IAC/C1B,QAAQ,CAACG,QAAQ,IAAIlB,gBAAgB,CAAC0C,SAAS,EAC/C;QACA,IAAI,CAAClC,eAAe,CAAC;UACnBa,EAAE,EAAEN,QAAQ,CAACM,EAAE;UACfC,GAAG,EAAEP,QAAQ,CAACO,GAAG;UACjBE,IAAI,EAAET,QAAQ,CAACS,IAAI;UACnBC,IAAI,EAAEV,QAAQ,CAACU,IAAI;UACnBU,WAAW,EAAEpB,QAAQ,EAAEoB,WAAW;UAClCjB,QAAQ,EAAEH,QAAQ,CAACG,QAAQ;UAC3BS,OAAO,EAAEZ,QAAQ,CAACY,OAAO;UACzBE,kBAAkB,EAAEd,QAAQ,CAACc,kBAAkB;UAC/CC,QAAQ,EAAEf,QAAQ,CAACe,QAAQ;UAC3BI,cAAc,EAAEnB,QAAQ,CAACmB,cAAc;UACvCS,QAAQ,EAAE5B,QAAQ,CAAC4B,QAAQ;UAC3BC,WAAW,EAAE7B,QAAQ,CAAC6B,WAAW;UACjCC,UAAU,EAAE9B,QAAQ,CAAC8B,UAAU;UAC/BC,OAAO,EAAE/B,QAAQ,CAAC+B,OAAO;UACzBR,WAAW,EAAEvB,QAAQ,CAACuB;SACK,CAAC;OAC/B,MAAM,IAAIvB,QAAQ,CAACG,QAAQ,IAAIlB,gBAAgB,CAAC+C,kBAAkB,EAAE;QACnE,IAAI,CAACvC,eAAe,CAAC;UACnBa,EAAE,EAAEN,QAAQ,CAACM,EAAE;UACfC,GAAG,EAAEP,QAAQ,CAACO,GAAG;UACjBE,IAAI,EAAET,QAAQ,CAACS,IAAI;UACnBC,IAAI,EAAEV,QAAQ,CAACU,IAAI;UACnBP,QAAQ,EAAEH,QAAQ,CAACG,QAAQ;UAC3BS,OAAO,EAAEZ,QAAQ,CAACY,OAAO;UACzBE,kBAAkB,EAAEd,QAAQ,CAACc,kBAAkB;UAC/CC,QAAQ,EAAEf,QAAQ,CAACe,QAAQ;UAC3BI,cAAc,EAAEnB,QAAQ,CAACmB,cAAc;UACvCS,QAAQ,EAAE5B,QAAQ,CAAC4B,QAAQ;UAC3BC,WAAW,EAAE7B,QAAQ,CAAC6B,WAAW;UACjCI,aAAa,EAAEjC,QAAQ,CAACiC,aAAa;UACrCV,WAAW,EAAEvB,QAAQ,CAACuB;SACG,CAAC;OAC7B,MAAM,IAAIvB,QAAQ,CAACG,QAAQ,IAAIlB,gBAAgB,CAACiD,MAAM,EAAE;QACvD,IAAI,CAACzC,eAAe,CAAC;UACnBa,EAAE,EAAEN,QAAQ,CAACM,EAAE;UACfC,GAAG,EAAEP,QAAQ,CAACO,GAAG;UACjBE,IAAI,EAAET,QAAQ,CAACS,IAAI;UACnBC,IAAI,EAAEV,QAAQ,CAACU,IAAI;UACnBP,QAAQ,EAAEH,QAAQ,CAACG,QAAQ;UAC3BS,OAAO,EAAEZ,QAAQ,CAACY,OAAO;UACzBE,kBAAkB,EAAEd,QAAQ,CAACc,kBAAkB;UAC/CC,QAAQ,EAAEf,QAAQ,CAACe,QAAQ;UAC3BI,cAAc,EAAEnB,QAAQ,CAACmB,cAAc;UACvCgB,MAAM,EAAEnC,QAAQ,CAACmC,MAAM;UACvBC,QAAQ,EAAEpC,QAAQ,CAACoC,QAAQ;UAC3BC,QAAQ,EAAErC,QAAQ,CAACqC;SACF,CAAC;OACrB,MAAM,IAAKrC,QAAQ,CAACG,QAAQ,GAAGlB,gBAAgB,CAACqD,WAAW,EAAG;QAC7D,IAAI,CAAC7C,eAAe,CAAC;UACnB8C,eAAe,EAAEvC,QAAQ,CAACuC,eAAe;UACzCC,aAAa,EAAExC,QAAQ,CAACwC,aAAa;UACrCrC,QAAQ,EAAEH,QAAQ,CAACG,QAAQ;UAC3BK,KAAK,EAAER,QAAQ,CAACQ,KAAK;UACrBO,QAAQ,EAAEf,QAAQ,CAACe,QAAQ;UAC3B0B,IAAI,EAAEzC,QAAQ,CAACyC,IAAI;UACnBC,QAAQ,EAAE1C,QAAQ,CAAC0C,QAAQ;UAC3BpC,EAAE,EAAEN,QAAQ,CAACM,EAAE;UACfqC,sBAAsB,EAAE3C,QAAQ,CAAC2C,sBAAsB;UACvDC,WAAW,EAAE5C,QAAQ,CAAC4C,WAAW;UACjCC,UAAU,EAAE7C,QAAQ,CAAC6C,UAAU;UAC/BC,iBAAiB,EAAE9C,QAAQ,CAAC8C,iBAAiB;UAC7CC,SAAS,EAAE/C,QAAQ,CAAC+C,SAAS;UAC7BxC,GAAG,EAAEP,QAAQ,CAACO,GAAG;UACjBG,IAAI,EAAEV,QAAQ,CAACU,IAAI;UACnBsC,EAAE,EAAEhD,QAAQ,CAACgD,EAAE;UACfC,MAAM,EAAEjD,QAAQ,CAACiD,MAAM;UACvBC,WAAW,EAAElD,QAAQ,CAACkD,WAAW;UACjCC,SAAS,EAAEnD,QAAQ,CAACmD,SAAS;UAC7BC,aAAa,EAAEpD,QAAQ,CAACoD;SACT,CAAC;;KAErB,MAAM;MACL,IAAI,CAAC3D,eAAe,CAAC,IAAI,CAAC;;EAE9B;EAAC,QAAA4D,CAAA,G;qBAtLUnE,eAAe;EAAA;EAAA,QAAAoE,EAAA,G;WAAfpE,eAAe;IAAAqE,OAAA,EAAfrE,eAAe,CAAAsE,IAAA;IAAAC,UAAA,EAFd;EAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}