import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { catchError } from 'rxjs/operators';
import { Attribute, AttributeDTO } from 'src/app/shared/model/attribute';
import { environment } from 'src/environments/environment';
@Injectable({
  providedIn: 'root',
})
export class AttributeApiService {
  private backendUrl: string = environment.backEndUrl;
  constructor(private http: HttpClient) {}

  /**
   * Create a new attribute in the database
   *
   * @param {Attribute} attribute The attribute to create
   * @returns {Observable<Attribute>} An Observable that emits an object representing the created attribute, or an error
   *
   * @memberOf AttributeApiService
   */
  createAttribute(attribute: AttributeDTO): Observable<AttributeDTO> {
    return this.http
      .post<AttributeDTO>(this.backendUrl + '/Attribute', attribute)
      .pipe(
        catchError((error) => {
          console.error('Error creating Attribute:', error);
          throw error;
        })
      );
  }

  /**
   * Update  an existing attribute in the database
   *
   * @param {Attribute} attribute  The attribute with updated information
   * @returns {Observable<Attribute>}  An observable that resolves to the updated attribute
   *
   * @memberOf AttributeApiService
   */
  updateAttribute(attribute: Attribute): Observable<Attribute> {
    return this.http
      .patch<Attribute>(this.backendUrl + '/Attribute', attribute)
      .pipe(
        catchError((error) => {
          console.error('Error updating attribute:', error);
          throw error;
        })
      );
  }

  /**
   * Delete an attribute from the database
   *
   * @param {number} attributeIds The ID of the attribute to delete
   * @returns {Observable<Attribute>} An observable that resolves to the deleted attribute
   *
   * @memberOf AttributeApiService
   */
  deleteAttributes(attributeIds: number[]): Observable<void> {
    return this.http
      .post<void>(this.backendUrl + `/Attribute/deleteMultiple`, attributeIds)
      .pipe(
        catchError((error) => {
          console.error('Error deleting attribute:', error);
          throw error;
        })
      );
  }
}
