{"ast": null, "code": "import packageJson from '../../package.json';\n// Constants for better maintainability\nconst APP_NAME = 'UML Web Application';\nconst RELEASE_DATE = '1747721940176';\n// GoJS License Key (consider moving to environment variables for production)\nconst GOJS_LICENSE_KEY = '298647e0b66143c702d90676423d6bbc5cf07e34cb960ef2050047f4ec5c6d47719bed7859c19bc681ab1bfd1f2ec78d8ac73e29c345553ab238dad842e581f8b53126b0115b408aa15424c190ff29a9a92d74f690e674a6d27888f6eaf891cb5ceca7d71bcf5ebc2e2d0f66507dff4be0f28e69e904991f6d6dcaf7fbfbbf4afb6f729b96fb578f';\n// Helper function to get auth URL\nconst getAuthUrl = () => {\n  if (typeof window !== 'undefined') {\n    return `${window.location.origin}/authentication`;\n  }\n  return '/authentication'; // Fallback for SSR\n};\nexport const environment = {\n  production: false,\n  backEndUrl: 'https://localhost:44380/api',\n  authUrl: getAuthUrl(),\n  about: {\n    appName: APP_NAME,\n    version: packageJson.version,\n    gojsVersion: packageJson.dependencies.gojs,\n    releaseDate: RELEASE_DATE\n  },\n  licenseKey: GOJS_LICENSE_KEY,\n  // WARNING: Remove bearerToken from production builds\n  // This should be handled through proper authentication flow\n  // Consider using environment variables or secure token storage\n  bearerToken: process.env['NODE_ENV'] === 'development' ? 'Bearer eyJhbGciOiJSUzI1NiIsImtpZCI6ImJhYTY0ZWZjMTNlZjIzNmJlOTIxZjkyMmUzYTY3Y2M5OTQxNWRiOWIiLCJ0eXAiOiJKV1QifQ.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.JLeu4Y4rWWylGLhOP9xlAFQHpk-40HQAu5CS9l2Dt6FP19qrBmt_bLKQgeMTlQON0qSro8qTeK36Um6T7mlb2sA9zclvIllp6eSn7wMOfl-9F_VP500_mo7m79KQmTlMLvJoSUVDFjT4WvUFjV94HAYFYynwHWkl6E13EgGO-D3kkTWA6-pBjBH5OEzmjPwGaYcdo8bKwWzVawDlMBDabiQVCRvNKgzZxKgnDjEhQ5vLAruu5A5PE-6tXP7srhexfWTKT4k0wdu7MwpPOQ9Nf8TNIA9ujbx-M9AvrEDwL7_9pBrumm5t9eQhyYTj0OeujlfYy2rIwYdspiqM-CrHDQ' : undefined\n};\n// Export individual configuration pieces for easier testing and modularity\nexport const API_CONFIG = {\n  backEndUrl: environment.backEndUrl,\n  authUrl: environment.authUrl\n};\nexport const APP_INFO = environment.about;", "map": {"version": 3, "names": ["packageJson", "APP_NAME", "RELEASE_DATE", "GOJS_LICENSE_KEY", "getAuthUrl", "window", "location", "origin", "environment", "production", "backEndUrl", "authUrl", "about", "appName", "version", "gojsVersion", "dependencies", "gojs", "releaseDate", "licenseKey", "bearerToken", "process", "env", "undefined", "API_CONFIG", "APP_INFO"], "sources": ["D:\\GitHub\\Bassetti\\devint-BASSETTI-GROUP-APP\\BassettiUMLWebApp\\UMLApp\\src\\environments\\environment.ts"], "sourcesContent": ["import packageJson from '../../package.json';\r\n\r\n// Environment configuration interface for type safety\r\nexport interface Environment {\r\n  production: boolean;\r\n  backEndUrl: string;\r\n  authUrl: string;\r\n  about: {\r\n    appName: string;\r\n    version: string;\r\n    gojsVersion: string;\r\n    releaseDate: string;\r\n  };\r\n  licenseKey: string;\r\n  bearerToken?: string; // Optional for development only\r\n}\r\n\r\n// Constants for better maintainability\r\nconst APP_NAME = 'UML Web Application';\r\nconst RELEASE_DATE = '1747721940176';\r\n\r\n// GoJS License Key (consider moving to environment variables for production)\r\nconst GOJS_LICENSE_KEY =\r\n  '298647e0b66143c702d90676423d6bbc5cf07e34cb960ef2050047f4ec5c6d47719bed7859c19bc681ab1bfd1f2ec78d8ac73e29c345553ab238dad842e581f8b53126b0115b408aa15424c190ff29a9a92d74f690e674a6d27888f6eaf891cb5ceca7d71bcf5ebc2e2d0f66507dff4be0f28e69e904991f6d6dcaf7fbfbbf4afb6f729b96fb578f';\r\n\r\n// Helper function to get auth URL\r\nconst getAuthUrl = (): string => {\r\n  if (typeof window !== 'undefined') {\r\n    return `${window.location.origin}/authentication`;\r\n  }\r\n  return '/authentication'; // Fallback for SSR\r\n};\r\n\r\nexport const environment: Environment = {\r\n  production: false,\r\n  backEndUrl: 'https://localhost:44380/api',\r\n  authUrl: getAuthUrl(),\r\n  about: {\r\n    appName: APP_NAME,\r\n    version: packageJson.version,\r\n    gojsVersion: packageJson.dependencies.gojs,\r\n    releaseDate: RELEASE_DATE,\r\n  },\r\n  licenseKey: GOJS_LICENSE_KEY,\r\n  // WARNING: Remove bearerToken from production builds\r\n  // This should be handled through proper authentication flow\r\n  // Consider using environment variables or secure token storage\r\n  bearerToken:\r\n    process.env['NODE_ENV'] === 'development'\r\n      ? 'Bearer eyJhbGciOiJSUzI1NiIsImtpZCI6ImJhYTY0ZWZjMTNlZjIzNmJlOTIxZjkyMmUzYTY3Y2M5OTQxNWRiOWIiLCJ0eXAiOiJKV1QifQ.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.JLeu4Y4rWWylGLhOP9xlAFQHpk-40HQAu5CS9l2Dt6FP19qrBmt_bLKQgeMTlQON0qSro8qTeK36Um6T7mlb2sA9zclvIllp6eSn7wMOfl-9F_VP500_mo7m79KQmTlMLvJoSUVDFjT4WvUFjV94HAYFYynwHWkl6E13EgGO-D3kkTWA6-pBjBH5OEzmjPwGaYcdo8bKwWzVawDlMBDabiQVCRvNKgzZxKgnDjEhQ5vLAruu5A5PE-6tXP7srhexfWTKT4k0wdu7MwpPOQ9Nf8TNIA9ujbx-M9AvrEDwL7_9pBrumm5t9eQhyYTj0OeujlfYy2rIwYdspiqM-CrHDQ'\r\n      : undefined,\r\n};\r\n\r\n// Export individual configuration pieces for easier testing and modularity\r\nexport const API_CONFIG = {\r\n  backEndUrl: environment.backEndUrl,\r\n  authUrl: environment.authUrl,\r\n} as const;\r\n\r\nexport const APP_INFO = environment.about;\r\n"], "mappings": "AAAA,OAAOA,WAAW,MAAM,oBAAoB;AAiB5C;AACA,MAAMC,QAAQ,GAAG,qBAAqB;AACtC,MAAMC,YAAY,GAAG,eAAe;AAEpC;AACA,MAAMC,gBAAgB,GACpB,kRAAkR;AAEpR;AACA,MAAMC,UAAU,GAAGA,CAAA,KAAa;EAC9B,IAAI,OAAOC,MAAM,KAAK,WAAW,EAAE;IACjC,OAAO,GAAGA,MAAM,CAACC,QAAQ,CAACC,MAAM,iBAAiB;;EAEnD,OAAO,iBAAiB,CAAC,CAAC;AAC5B,CAAC;AAED,OAAO,MAAMC,WAAW,GAAgB;EACtCC,UAAU,EAAE,KAAK;EACjBC,UAAU,EAAE,6BAA6B;EACzCC,OAAO,EAAEP,UAAU,EAAE;EACrBQ,KAAK,EAAE;IACLC,OAAO,EAAEZ,QAAQ;IACjBa,OAAO,EAAEd,WAAW,CAACc,OAAO;IAC5BC,WAAW,EAAEf,WAAW,CAACgB,YAAY,CAACC,IAAI;IAC1CC,WAAW,EAAEhB;GACd;EACDiB,UAAU,EAAEhB,gBAAgB;EAC5B;EACA;EACA;EACAiB,WAAW,EACTC,OAAO,CAACC,GAAG,CAAC,UAAU,CAAC,KAAK,aAAa,GACrC,08BAA08B,GAC18BC;CACP;AAED;AACA,OAAO,MAAMC,UAAU,GAAG;EACxBd,UAAU,EAAEF,WAAW,CAACE,UAAU;EAClCC,OAAO,EAAEH,WAAW,CAACG;CACb;AAEV,OAAO,MAAMc,QAAQ,GAAGjB,WAAW,CAACI,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}