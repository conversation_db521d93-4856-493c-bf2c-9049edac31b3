{"ast": null, "code": "import { computed, signal } from '@angular/core';\nimport { Subject } from 'rxjs';\nimport { AttributeMemberType } from 'src/app/shared/model/attribute';\nimport { GoJsNodeIcon } from 'src/app/shared/model/common';\nimport { GojsNodeCategory } from 'src/app/shared/model/gojs';\nimport { TreeNodeTag } from 'src/app/shared/model/treeNode';\nimport { ClassWrapperCategory, DiagramWrapperCategory, DiagramWrapperName, EnumWrapperCategory } from 'src/app/shared/utils/constants';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../data-format/data-format.service\";\nimport * as i2 from \"../class/class.service\";\nimport * as i3 from \"../enumeration/enumeration.service\";\nimport * as i4 from \"../folder/folder.service\";\nimport * as i5 from \"src/app/shared/utils/diagram-utils\";\nexport class TreeNodeService {\n  constructor(dataFormatService, _classService, _enumerationService, _folderService, _diagramUtils) {\n    this.dataFormatService = dataFormatService;\n    this._classService = _classService;\n    this._enumerationService = _enumerationService;\n    this._folderService = _folderService;\n    this._diagramUtils = _diagramUtils;\n    // Signal for library details\n    this.libraryDetailsSignal = signal(null);\n    // Subject for backward compatibility\n    this.libraryDetailsSubject = new Subject();\n    // Computed signal for descendant nodes\n    this.descendantTreeNodes = computed(() => {\n      const libraryDetails = this.libraryDetailsSignal();\n      if (!libraryDetails) return [];\n      return this.getDescendantNodes(libraryDetails);\n    });\n    this.currentDiagramId = -1;\n    this.CATEGORY_PRIORITY = [DiagramWrapperCategory, ClassWrapperCategory, EnumWrapperCategory, GojsNodeCategory.Folder // Always at the end\n    ];\n    this._diagramUtils.activeDiagramChanges().subscribe(diagram => {\n      if (diagram && diagram.id) this.currentDiagramId = diagram.id;\n    });\n  }\n  getLibraryDetails() {\n    return this.libraryDetailsSubject.asObservable();\n  }\n  setLibraryDetails(projectDetails) {\n    const formattedNode = this.formatTreeData(projectDetails);\n    this.libraryDetailsSignal.set(formattedNode);\n    this.libraryDetailsSubject.next(formattedNode);\n  }\n  getDescendantNodes(node) {\n    const descendants = [];\n    const traverse = currentNode => {\n      if (currentNode.children) {\n        currentNode.children.forEach(child => {\n          descendants.push(child);\n          traverse(child);\n        });\n      }\n    };\n    traverse(node);\n    return descendants;\n  }\n  getWrapperParentTag(wrapperTag) {\n    return `${wrapperTag}_${TreeNodeTag.Project}`;\n  }\n  formatTreeData(projectDetails) {\n    const treeNode = {\n      name: projectDetails.name,\n      category: GojsNodeCategory.Project,\n      children: [...(projectDetails.diagrams.length > 0 ? [this.formatDiagramTreeNode(projectDetails.diagrams, TreeNodeTag.Project, projectDetails.id)] : []), ...this.formatClassAndEnum(projectDetails.templateClasses, projectDetails.templateEnumerations, TreeNodeTag.Project), ...this.formatFoldersRecursively(projectDetails.folders, TreeNodeTag.Project, projectDetails.id)],\n      tag: TreeNodeTag.Project,\n      icon: GoJsNodeIcon.Project,\n      supportingNodes: [GojsNodeCategory.Class, GojsNodeCategory.AssociativeClass, GojsNodeCategory.Enumeration, GojsNodeCategory.Folder, GojsNodeCategory.Diagram]\n    };\n    return treeNode;\n  }\n  formatFoldersRecursively(folders, parentTag, projectId) {\n    return folders.map(folder => {\n      const children = [...this.formatClassAndEnum(folder.templateClasses, folder.templateEnumerations, `atTag${GojsNodeCategory.Folder}_${folder.id}`), ...this.sortTreeNodeChildren(this.formatFoldersRecursively(folder.childFolders || [], `atTag${GojsNodeCategory.Folder}_${folder.id}`, projectId))];\n      // Add diagrams to children only if their length is greater than zero\n      if (folder.diagrams.length > 0) {\n        children.unshift(this.formatDiagramTreeNode(folder.diagrams, `atTag${GojsNodeCategory.Folder}_${folder.id}`, projectId));\n      }\n      return {\n        name: folder.name,\n        children: children,\n        category: GojsNodeCategory.Folder,\n        icon: GoJsNodeIcon.Folder,\n        data: this.dataFormatService.formatFolderData(folder),\n        tag: `atTag${GojsNodeCategory.Folder}_${folder.id}`,\n        parentTag: parentTag,\n        isDraggable: true,\n        supportingNodes: [GojsNodeCategory.Class, GojsNodeCategory.AssociativeClass, GojsNodeCategory.Enumeration, GojsNodeCategory.Folder, GojsNodeCategory.Diagram]\n      };\n    });\n  }\n  formatClassAndEnum(templateClasses, templateEnumerations, parentTag) {\n    const treeNodes = [];\n    if (templateClasses.length > 0) {\n      treeNodes.push({\n        name: 'Classes',\n        children: this.sortTreeNodeChildren(templateClasses.map(tempClass => ({\n          name: tempClass.name,\n          children: this.sortTreeNodeChildren(this.formatAttributeNode(tempClass)),\n          category: tempClass.isAssociative ? GojsNodeCategory.AssociativeClass : GojsNodeCategory.Class,\n          tag: `atTag${GojsNodeCategory.Class}_${tempClass.id}`,\n          icon: tempClass.isAssociative ? GoJsNodeIcon.Associative : GoJsNodeIcon.Class,\n          parentTag: `${TreeNodeTag.ClassWrapper}_${parentTag}`,\n          data: this.dataFormatService.formatDiagramClassNode(tempClass, this.dataFormatService.formatAttributeData(tempClass.attributes || [])),\n          isDraggable: true,\n          supportingNodes: [GojsNodeCategory.Operation, GojsNodeCategory.Attribute]\n        }))),\n        supportingNodes: [GojsNodeCategory.Class, GojsNodeCategory.AssociativeClass],\n        category: ClassWrapperCategory,\n        tag: `${TreeNodeTag.ClassWrapper}_${parentTag}`,\n        parentTag: parentTag,\n        icon: GoJsNodeIcon.Class\n      });\n    }\n    if (templateEnumerations.length > 0) {\n      treeNodes.push({\n        name: 'Enumerations',\n        children: this.sortTreeNodeChildren(this.getTemplateEnumsForTree(templateEnumerations, parentTag)),\n        category: EnumWrapperCategory,\n        icon: GoJsNodeIcon.Enumeration,\n        tag: `${TreeNodeTag.EnumerationWrapper}_${parentTag}`,\n        parentTag: parentTag,\n        supportingNodes: [GojsNodeCategory.Enumeration]\n      });\n    }\n    return treeNodes;\n  }\n  formatDiagramTreeNode(diagrams, parentTag, projectId) {\n    return {\n      name: DiagramWrapperName,\n      children: diagrams.map(diagram => ({\n        name: diagram.name,\n        children: [],\n        category: GojsNodeCategory.Diagram,\n        icon: GoJsNodeIcon.Diagram,\n        tag: `atTag${GojsNodeCategory.Diagram}_${diagram.id}`,\n        data: {\n          ...diagram,\n          idProject: projectId\n        },\n        parentTag: `${TreeNodeTag.DiagramWrapper}_${parentTag}`,\n        isDraggable: true\n      })),\n      tag: `${TreeNodeTag.DiagramWrapper}_${parentTag}`,\n      parentTag: parentTag,\n      category: DiagramWrapperCategory,\n      icon: GoJsNodeIcon.Diagram,\n      supportingNodes: [GojsNodeCategory.Diagram]\n    };\n  }\n  getTemplateEnumsForTree(templateEnumerations, parentTag) {\n    return templateEnumerations.map(tempEnum => {\n      // add AttributeTypes for each tempEnum\n      this._diagramUtils.addAttributeTypes({\n        id: tempEnum.id?.toString(),\n        name: tempEnum?.name,\n        isEnumeration: true\n      });\n      // Return the formatted tree node for each tempEnum\n      return {\n        name: tempEnum.name,\n        children: this.formatLiteralTreeNode(tempEnum),\n        category: GojsNodeCategory.Enumeration,\n        tag: `atTag${GojsNodeCategory.Enumeration}_${tempEnum.id}`,\n        parentTag: `${TreeNodeTag.EnumerationWrapper}_${parentTag}`,\n        icon: GoJsNodeIcon.Enumeration,\n        data: this.dataFormatService.formatDiagramEnumData(tempEnum, this.dataFormatService.formatLiteralData(tempEnum.enumerationLiterals || [])),\n        isDraggable: true,\n        supportingNodes: [GojsNodeCategory.EnumerationLiteral]\n      };\n    });\n  }\n  formatLiteralTreeNode(tempEnum) {\n    return tempEnum.enumerationLiterals?.map(literal => ({\n      name: literal.name,\n      children: [],\n      category: GojsNodeCategory.EnumerationLiteral,\n      icon: GoJsNodeIcon.EnumerationLiteral,\n      tag: `atTag${GojsNodeCategory.EnumerationLiteral}_${literal.id}`,\n      parentTag: `atTag${GojsNodeCategory.Enumeration}_${tempEnum.id}`,\n      supportingNodes: [],\n      data: {\n        ...this.dataFormatService.formatLiteralData([literal])[0],\n        idTemplateEnumeration: tempEnum.id\n      }\n    })) || [];\n  }\n  formatAttributeNode(tempClass) {\n    return tempClass.attributes.map(attr => ({\n      name: attr.name,\n      children: [],\n      category: attr.category == AttributeMemberType.attribute ? GojsNodeCategory.Attribute : GojsNodeCategory.Operation,\n      icon: attr.category == AttributeMemberType.attribute ? GoJsNodeIcon.Attribute : GoJsNodeIcon.Operation,\n      tag: `atTag${attr.category == AttributeMemberType.attribute ? GojsNodeCategory.Attribute : GojsNodeCategory.Operation}_${attr.id}`,\n      parentTag: `atTag${GojsNodeCategory.Class}_${tempClass.id}`,\n      data: {\n        ...this.dataFormatService.formatAttributeData([attr])[0],\n        idTemplateClass: tempClass.id\n      },\n      supportingNodes: []\n    }));\n  }\n  /**\n   * Adds a group node to the tree structure by either appending it to a parent node\n   * or creating a wrapper node if a suitable parent is not found. The method handles\n   * nodes based on their category, organizing `Folder` nodes separately from other types.\n   * After insertion, the tree nodes are sorted to maintain a defined order.\n   * @param nodeData - The data for the node to be added to the tree.\n   *                   It contains the node's details, such as category and parent tag.\n   */\n  addGroupNodeInTree(nodeData) {\n    const treeNode = this.libraryDetailsSignal();\n    if (!treeNode) return;\n    const updatedTreeNode = {\n      ...treeNode\n    };\n    const isProjectOrFolderNode = nodeData.category === GojsNodeCategory.Folder && nodeData.parentTag === TreeNodeTag.Project;\n    if (isProjectOrFolderNode) {\n      this.addNodeToChildren(nodeData, updatedTreeNode.children);\n    } else {\n      const parentNode = this.findNodeByTag(nodeData.parentTag);\n      if (parentNode) {\n        this.addNodeToParent(nodeData, parentNode);\n      } else {\n        // If parent not found, add to root\n        this.getOrCreateWrapperNode(nodeData, updatedTreeNode);\n      }\n    }\n    // Ensure the tree structure is sorted and updated\n    updatedTreeNode.children = this.sortTreeNodes(updatedTreeNode.children);\n    this.libraryDetailsSignal.set(updatedTreeNode);\n    this.libraryDetailsSubject.next(updatedTreeNode);\n  }\n  addNodeToChildren(node, children) {\n    children.push(node);\n    this.sortTreeNodeChildren(children);\n  }\n  addNodeToParent(node, parentNode) {\n    if (parentNode.category === GojsNodeCategory.Folder) {\n      if (node.category !== GojsNodeCategory.Folder) {\n        this.getOrCreateWrapperNode(node, parentNode);\n      } else {\n        this.addNodeToChildren(node, parentNode.children);\n      }\n    } else {\n      this.addNodeToChildren(node, parentNode.children);\n    }\n  }\n  /**\n   * Sorts an array of tree nodes based on the priority of their categories.\n   * The priority order is defined in `CATEGORY_PRIORITY`. Nodes with undefined\n   * categories are placed at the end of the list.\n   *\n   * @param nodes - An array of `TreeNode` objects to be sorted.\n   * @returns A sorted array of `TreeNode` objects, ordered by their category priority.\n   */\n  sortTreeNodes(nodes) {\n    return nodes.sort((a, b) => {\n      // Get the priority index of each node's category\n      const indexA = this.CATEGORY_PRIORITY.indexOf(a.category);\n      const indexB = this.CATEGORY_PRIORITY.indexOf(b.category);\n      // Place nodes with unknown categories at the end\n      if (indexA === -1) return 1;\n      if (indexB === -1) return -1;\n      // Sort by priority index in ascending order\n      return indexA - indexB;\n    });\n  }\n  sortTreeNodeChildren(nodes) {\n    return nodes.sort((a, b) => a.name.localeCompare(b.name, undefined, {\n      sensitivity: 'base'\n    }));\n  }\n  /**\n   * Ensures that a wrapper node exists for the specified `targetedNode` within the `parentNode`.\n   * If the wrapper node already exists, the `targetedNode` is added to its children.\n   * Otherwise, a new wrapper node is created, added to the `parentNode`, and the `targetedNode`\n   * is added as its child.\n   *\n   * @param targetedNode - The `TreeNode` that needs to be added to a wrapper node.\n   * @param parentNode - The parent `TreeNode` under which the wrapper node will be created or updated.\n   */\n  getOrCreateWrapperNode(targetedNode, parentNode) {\n    // Attempt to find an existing wrapper node within the parent's children\n    let wrapperNode = parentNode.children.find(node => node.tag == `${targetedNode.category == GojsNodeCategory.Class || targetedNode.category == GojsNodeCategory.AssociativeClass ? TreeNodeTag.ClassWrapper : targetedNode.category === GojsNodeCategory.Diagram ? TreeNodeTag.DiagramWrapper : TreeNodeTag.EnumerationWrapper}_${targetedNode.parentTag}`);\n    if (wrapperNode) {\n      // If the wrapper node exists, add the targeted node as its child\n      wrapperNode.children.push(targetedNode);\n      this.sortTreeNodeChildren(wrapperNode.children);\n    } else {\n      // Create a new wrapper node if it doesn't exist\n      wrapperNode = this.constructWrapperNode(targetedNode, parentNode.tag);\n      parentNode?.children.push({\n        ...wrapperNode,\n        children: this.sortTreeNodeChildren([...wrapperNode.children, targetedNode])\n      });\n      this.sortTreeNodes(parentNode.children);\n    }\n  }\n  addItemNodeInParent(itemNode) {\n    const treeNode = this.libraryDetailsSignal();\n    if (!treeNode) return;\n    const parentNode = this.findNodeByTag(itemNode.parentTag);\n    if (parentNode) {\n      parentNode.data.items.push(itemNode.data);\n      parentNode.children.push(itemNode);\n      this.sortTreeNodeChildren(parentNode.children);\n      // Create a new tree instance to trigger change detection\n      const updatedTree = this.cloneTreeNode(treeNode);\n      this.libraryDetailsSignal.set(updatedTree);\n      this.libraryDetailsSubject.next(updatedTree);\n    }\n  }\n  editGroupTreeNode(treeNode) {\n    const updatedLibraryDetails = this.libraryDetailsSignal();\n    if (!updatedLibraryDetails) return;\n    const clonedTree = this.cloneTreeNode(updatedLibraryDetails);\n    const parentNode = this.findParentNode(treeNode.tag, clonedTree);\n    if (parentNode) {\n      const nodeToUpdate = parentNode?.children.find(node => node.tag === treeNode.tag);\n      if (nodeToUpdate && nodeToUpdate.data && treeNode.data) {\n        nodeToUpdate.name = treeNode.data.name;\n        if (nodeToUpdate.category === GojsNodeCategory.Attribute || nodeToUpdate.category === GojsNodeCategory.Operation || nodeToUpdate.category === GojsNodeCategory.EnumerationLiteral) {\n          this.updateItemInClassOrEnum(parentNode, treeNode, clonedTree);\n          if (nodeToUpdate.category == GojsNodeCategory.Attribute || nodeToUpdate.category === GojsNodeCategory.Operation) {\n            const itemNode = nodeToUpdate.data;\n            this.updateNodeData(nodeToUpdate.data, {\n              name: itemNode.name,\n              id: itemNode.id,\n              description: itemNode.description,\n              dataType: itemNode.dataType\n            });\n          } else {\n            const itemNode = nodeToUpdate.data;\n            this.updateNodeData(nodeToUpdate.data, {\n              name: itemNode.name,\n              id: itemNode.id\n            });\n          }\n        } else {\n          const classOrEnumNode = treeNode.data;\n          this.updateNodeData(nodeToUpdate.data, {\n            name: classOrEnumNode.name,\n            id: classOrEnumNode.id,\n            color: classOrEnumNode.color,\n            description: classOrEnumNode.description,\n            tag: classOrEnumNode.tag,\n            volumetry: classOrEnumNode.volumetry,\n            treeNodeTag: classOrEnumNode.treeNodeTag,\n            position: classOrEnumNode.position,\n            size: classOrEnumNode.size\n          });\n        }\n        this.sortTreeNodeChildren(parentNode?.children);\n        this.sortTreeNodes(parentNode?.children);\n        this.libraryDetailsSignal.set(clonedTree);\n        this.libraryDetailsSubject.next(clonedTree);\n      }\n    }\n  }\n  updateNodeData(nodeToUpdate, treeNodeData) {\n    Object.keys(treeNodeData).forEach(key => {\n      if (key in nodeToUpdate) {\n        nodeToUpdate[key] = treeNodeData[key];\n      }\n    });\n  }\n  updateItemInClassOrEnum(groupNode, treeNode, libraryDetails) {\n    if (groupNode.data && treeNode.data) {\n      groupNode.data.items.forEach(item => {\n        if (item.id == treeNode.data?.id) {\n          Object.assign(item, treeNode.data);\n        }\n      });\n      this.libraryDetailsSignal.set(libraryDetails);\n      this.libraryDetailsSubject.next(libraryDetails);\n    }\n  }\n  deleteGroupTreeNode(treeNode) {\n    const updatedLibraryDetails = this.libraryDetailsSignal();\n    if (!updatedLibraryDetails) return;\n    const clonedTree = this.cloneTreeNode(updatedLibraryDetails);\n    const parentNode = this.findParentNode(treeNode.tag, clonedTree);\n    if (parentNode) {\n      const index = parentNode?.children.findIndex(node => node.tag === treeNode.tag);\n      if (treeNode.category === GojsNodeCategory.Attribute || treeNode.category === GojsNodeCategory.Operation || treeNode.category === GojsNodeCategory.EnumerationLiteral) {\n        parentNode.data.items = parentNode.data.items.filter(item => item.id !== treeNode.data?.id);\n      }\n      if (index > -1) {\n        parentNode?.children.splice(index, 1);\n        if (parentNode.children.length == 0 && (parentNode.category == ClassWrapperCategory || parentNode.category == EnumWrapperCategory || parentNode.category == DiagramWrapperCategory)) {\n          const emptyWrapperParentNode = this.findParentNode(parentNode.tag, clonedTree);\n          if (emptyWrapperParentNode) {\n            emptyWrapperParentNode.children = emptyWrapperParentNode.children.filter(child => child.tag !== parentNode.tag);\n          }\n        }\n        this.libraryDetailsSignal.set(clonedTree);\n        this.libraryDetailsSubject.next(clonedTree);\n      }\n    }\n  }\n  moveNode(targetFolder, draggedNode) {\n    // Get the current value of library details\n    const updatedLibraryDetails = this.libraryDetailsSignal();\n    if (!updatedLibraryDetails) return;\n    const clonedTree = this.cloneTreeNode(updatedLibraryDetails);\n    // Find and remove the node from its current parent's children array\n    const parentNode = this.findParentNode(draggedNode.tag, clonedTree);\n    if (!this.checkDropValidation(targetFolder, draggedNode, parentNode, clonedTree)) return;\n    if (parentNode) {\n      parentNode.children = parentNode.children.filter(child => child.tag !== draggedNode.tag);\n      if (parentNode.children.length == 0 && (parentNode.category == ClassWrapperCategory || parentNode.category == EnumWrapperCategory || parentNode.category == DiagramWrapperCategory)) {\n        const emptyWrapperParentNode = this.findParentNode(parentNode.tag, clonedTree);\n        if (emptyWrapperParentNode) {\n          emptyWrapperParentNode.children = emptyWrapperParentNode.children.filter(child => child.tag !== parentNode.tag);\n        }\n      }\n    }\n    // Add the node to the target folder's children array\n    if (draggedNode.category === GojsNodeCategory.Folder || targetFolder.category == ClassWrapperCategory || targetFolder.category == EnumWrapperCategory || targetFolder.category == DiagramWrapperCategory) {\n      targetFolder.children.push({\n        ...draggedNode,\n        parentTag: targetFolder.tag\n      });\n      this.sortTreeNodeChildren(targetFolder.children);\n      this.sortTreeNodes(targetFolder.children);\n      this.moveNodeToFolder(targetFolder, draggedNode);\n    } else {\n      const targetFolderNode = this.findNodeByTagInTree(targetFolder.tag, clonedTree);\n      if (targetFolderNode) {\n        const wrapperNode = this.constructWrapperNode(draggedNode, targetFolderNode.tag);\n        const targetedWrapperNode = targetFolderNode?.children.find(node => node.tag === wrapperNode.tag);\n        this.moveNodeToFolder(targetFolderNode, draggedNode);\n        if (targetedWrapperNode) {\n          targetedWrapperNode.children.push({\n            ...draggedNode,\n            parentTag: targetedWrapperNode.tag\n          });\n          this.sortTreeNodeChildren(targetedWrapperNode.children);\n        } else {\n          targetFolderNode.children.push({\n            ...wrapperNode,\n            children: [...wrapperNode.children, {\n              ...draggedNode,\n              parentTag: wrapperNode.tag\n            }],\n            parentTag: targetFolder.tag\n          });\n          this.sortTreeNodes(targetFolderNode.children);\n        }\n      }\n    }\n    // Update the Subject with the modified library details\n    this.libraryDetailsSignal.set(clonedTree);\n    this.libraryDetailsSubject.next(clonedTree);\n  }\n  checkDropValidation(targetNode, draggedNode, parentNode, libraryDetails) {\n    if (parentNode?.parentTag === libraryDetails?.tag && targetNode.category === GojsNodeCategory.Project && draggedNode.category !== GojsNodeCategory.Folder) {\n      return false;\n    }\n    if (draggedNode.tag === targetNode.tag || draggedNode.tag == targetNode.parentTag || draggedNode.parentTag == targetNode.tag) return false;\n    if (targetNode.category === GojsNodeCategory.Diagram) return false;\n    // Check if the current parent is the dragged node\n    let currentParent = this.findParentNode(targetNode.tag, libraryDetails);\n    if ((targetNode.category == ClassWrapperCategory || targetNode.category == DiagramWrapperCategory || targetNode.category == EnumWrapperCategory) && currentParent?.category == GojsNodeCategory.Folder) {\n      return false;\n    }\n    while (currentParent) {\n      if (currentParent.tag === draggedNode.tag) {\n        return false; // Found an ancestor\n      }\n      currentParent = this.findParentNode(currentParent.tag, libraryDetails);\n    }\n    return true;\n  }\n  findParentNode(nodeTag, folder) {\n    if (folder.children) {\n      for (let child of folder.children) {\n        if (child.tag === nodeTag) {\n          return folder;\n        }\n        const node = this.findParentNode(nodeTag, child);\n        if (node) return node;\n      }\n    }\n    return null;\n  }\n  findNodeByTag(tag) {\n    if (tag == TreeNodeTag.Project) {\n      return this.libraryDetailsSignal();\n    }\n    const descendants = this.descendantTreeNodes();\n    return descendants.find(node => node.tag == tag) || null;\n  }\n  findNodeByTagInTree(tag, tree) {\n    if (tree.tag === tag) {\n      return tree;\n    }\n    if (tree.children) {\n      for (const child of tree.children) {\n        const found = this.findNodeByTagInTree(tag, child);\n        if (found) return found;\n      }\n    }\n    return null;\n  }\n  constructWrapperNode(draggedNode, parentTag) {\n    const wrapperNodeName = draggedNode.category === GojsNodeCategory.Class || draggedNode.category === GojsNodeCategory.AssociativeClass ? 'Classes' : draggedNode.category === GojsNodeCategory.Diagram ? 'Diagrams' : 'Enumerations';\n    const wrapperNodeTag = draggedNode.category === GojsNodeCategory.Class || draggedNode.category === GojsNodeCategory.AssociativeClass ? TreeNodeTag.ClassWrapper : draggedNode.category === GojsNodeCategory.Diagram ? TreeNodeTag.DiagramWrapper : TreeNodeTag.EnumerationWrapper;\n    const wrapperNodeCategory = draggedNode.category === GojsNodeCategory.Class || draggedNode.category === GojsNodeCategory.AssociativeClass ? ClassWrapperCategory : draggedNode.category === GojsNodeCategory.Diagram ? DiagramWrapperCategory : EnumWrapperCategory;\n    const wrapperNodeIcon = draggedNode.category === GojsNodeCategory.Class || draggedNode.category === GojsNodeCategory.AssociativeClass ? GoJsNodeIcon.Class : draggedNode.category === GojsNodeCategory.Diagram ? GoJsNodeIcon.Diagram : GoJsNodeIcon.Enumeration;\n    return {\n      name: wrapperNodeName,\n      children: [],\n      category: wrapperNodeCategory,\n      tag: `${wrapperNodeTag}_${parentTag}`,\n      parentTag: parentTag,\n      icon: wrapperNodeIcon,\n      supportingNodes: [draggedNode.category]\n    };\n  }\n  moveNodeToFolder(targetNode, draggedNode) {\n    if (draggedNode.data) {\n      if (targetNode.category === GojsNodeCategory.Folder) {\n        if (draggedNode.category === GojsNodeCategory.Class) {\n          this._classService.moveTempClassToFolder({\n            id: (draggedNode?.data).idTemplateClass,\n            idFolder: (targetNode?.data).idFolder\n          }).subscribe();\n        } else if (draggedNode.category === GojsNodeCategory.Enumeration) {\n          this._enumerationService.moveTempEnumToFolder({\n            id: (draggedNode?.data).idTemplateEnumeration,\n            idFolder: (targetNode?.data).idFolder\n          }).subscribe();\n        } else if (draggedNode.category === GojsNodeCategory.Diagram) {\n          this._folderService.moveDiagramToFolder({\n            id: (draggedNode?.data).id,\n            idFolder: (targetNode?.data).idFolder\n          });\n        } else if (draggedNode.category === GojsNodeCategory.Folder) {\n          this._folderService.moveFolderToFolder({\n            id: (draggedNode?.data).idFolder,\n            parentFolderId: (targetNode?.data).idFolder\n          });\n        }\n      } else {\n        if (draggedNode.category === GojsNodeCategory.Class) {\n          this._classService.removeTempClassFromFolder((draggedNode?.data).idTemplateClass).subscribe();\n        } else if (draggedNode.category === GojsNodeCategory.Enumeration) {\n          this._enumerationService.removeTempEnumFromFolder((draggedNode?.data).idTemplateEnumeration).subscribe();\n        } else if (draggedNode.category === GojsNodeCategory.Diagram) {\n          this._folderService.removeDiagramFromFolder((draggedNode?.data).id);\n        } else if (draggedNode.category === GojsNodeCategory.Folder) {\n          this._folderService.removeFolderFromFolder((draggedNode?.data).idFolder);\n        }\n      }\n    }\n  }\n  getClassesEnumsFromFolder(folderNode) {\n    const nodes = [];\n    // Recursive function to collect classes, enums, and folders from the target folder\n    const collectChildNodes = node => {\n      node.children.forEach(child => {\n        if (child.category === ClassWrapperCategory || child.category === EnumWrapperCategory) {\n          nodes.push(...child.children);\n        } else if (child.category === GojsNodeCategory.Folder) {\n          collectChildNodes(child);\n        }\n      });\n    };\n    // Start collecting from the folder node\n    collectChildNodes(folderNode);\n    return nodes;\n  }\n  /**\n   * Deletes a diagram node by tag.\n   * @param {string} tag - Unique identifier for the node.\n   * @memberof TreeNodeService\n   * @returns {void}\n   */\n  deleteDiagram(tag) {\n    const node = this.findNodeByTag(tag);\n    if (node) this.deleteGroupTreeNode(node);\n  }\n  nodeExistOrNot(parentTag, nodes) {\n    const libraryDetails = this.libraryDetailsSignal();\n    if (libraryDetails) {\n      const parentNode = this.findParentNode(parentTag, libraryDetails);\n      if (parentNode) {\n        if (parentNode.category == GojsNodeCategory.Folder) {\n          if (nodes.some(node => node.tag === parentNode.tag)) return true;else return this.nodeExistOrNot(parentNode.tag, nodes);\n        }\n        return nodes.some(node => node.tag === parentNode.tag);\n      } else return false;\n    } else return false;\n  }\n  findCurrentDiagramParentNode() {\n    const currentDiagramTag = `atTag${GojsNodeCategory.Diagram}_${this.currentDiagramId}`;\n    const libraryDetails = this.libraryDetailsSignal();\n    if (libraryDetails) {\n      const wrapperNode = this.findParentNode(currentDiagramTag, libraryDetails);\n      if (wrapperNode) {\n        return this.findNodeByTag(wrapperNode.parentTag);\n      } else return null;\n    } else return null;\n  }\n  /**\n   * Deep clones a tree node and all its children\n   * @param node The node to clone\n   * @returns A deep copy of the node\n   */\n  cloneTreeNode(node) {\n    const cloned = {\n      ...node,\n      children: node.children ? node.children.map(child => this.cloneTreeNode(child)) : [],\n      data: node.data ? {\n        ...node.data\n      } : undefined\n    };\n    return cloned;\n  }\n  static #_ = this.ɵfac = function TreeNodeService_Factory(t) {\n    return new (t || TreeNodeService)(i0.ɵɵinject(i1.DataFormatService), i0.ɵɵinject(i2.ClassService), i0.ɵɵinject(i3.EnumerationService), i0.ɵɵinject(i4.FolderService), i0.ɵɵinject(i5.DiagramUtils));\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: TreeNodeService,\n    factory: TreeNodeService.ɵfac,\n    providedIn: 'root'\n  });\n}", "map": {"version": 3, "names": ["computed", "signal", "Subject", "AttributeMemberType", "GoJsNodeIcon", "GojsNodeCategory", "TreeNodeTag", "ClassWrapperCategory", "DiagramWrapperCategory", "DiagramWrapperName", "EnumWrapperCategory", "TreeNodeService", "constructor", "dataFormatService", "_classService", "_enumerationService", "_folderService", "_diagramUtils", "libraryDetailsSignal", "libraryDetailsSubject", "descendantTreeNodes", "libraryDetails", "getDescendantNodes", "currentDiagramId", "CATEGORY_PRIORITY", "Folder", "activeDiagramChanges", "subscribe", "diagram", "id", "getLibraryDetails", "asObservable", "setLibraryDetails", "projectDetails", "formattedNode", "formatTreeData", "set", "next", "node", "descendants", "traverse", "currentNode", "children", "for<PERSON>ach", "child", "push", "getWrapperParentTag", "wrapperTag", "Project", "treeNode", "name", "category", "diagrams", "length", "formatDiagramTreeNode", "formatClassAndEnum", "templateClasses", "templateEnumerations", "formatFoldersRecursively", "folders", "tag", "icon", "supportingNodes", "Class", "AssociativeClass", "Enumeration", "Diagram", "parentTag", "projectId", "map", "folder", "sortTreeNodeChildren", "childFolders", "unshift", "data", "formatFolderData", "isDraggable", "treeNodes", "tempClass", "formatAttributeNode", "isAssociative", "Associative", "ClassWrapper", "formatDiagramClassNode", "formatAttributeData", "attributes", "Operation", "Attribute", "getTemplateEnumsForTree", "EnumerationWrapper", "idProject", "DiagramWrapper", "tempEnum", "addAttributeTypes", "toString", "isEnumeration", "formatLiteralTreeNode", "formatDiagramEnumData", "formatLiteralData", "enumerationLiterals", "EnumerationLiteral", "literal", "idTemplateEnumeration", "attr", "attribute", "idTemplateClass", "addGroupNodeInTree", "nodeData", "updatedTreeNode", "isProjectOrFolderNode", "addNodeToChildren", "parentNode", "findNodeByTag", "addNodeToParent", "getOrCreateWrapperNode", "sortTreeNodes", "nodes", "sort", "a", "b", "indexA", "indexOf", "indexB", "localeCompare", "undefined", "sensitivity", "targetedNode", "wrapperNode", "find", "constructWrapperNode", "addItemNodeInParent", "itemNode", "items", "updatedTree", "cloneTreeNode", "editGroupTreeNode", "updatedLibraryDetails", "cloned<PERSON>ree", "findParentNode", "nodeToUpdate", "updateItemInClassOrEnum", "updateNodeData", "description", "dataType", "classOrEnumNode", "color", "volumetry", "treeNodeTag", "position", "size", "treeNodeData", "Object", "keys", "key", "groupNode", "item", "assign", "deleteGroupTreeNode", "index", "findIndex", "filter", "splice", "emptyWrapperParentNode", "moveNode", "targetFolder", "draggedNode", "checkDropValidation", "moveNodeToFolder", "targetFolderNode", "findNodeByTagInTree", "targetedWrapperNode", "targetNode", "currentParent", "nodeTag", "tree", "found", "wrapperNodeName", "wrapperNodeTag", "wrapperNodeCategory", "wrapperNodeIcon", "moveTempClassToFolder", "idFolder", "moveTempEnumToFolder", "moveDiagramToFolder", "moveFolderToFolder", "parentFolderId", "removeTempClassFromFolder", "removeTempEnumFromFolder", "removeDiagramFromFolder", "removeFolderFromFolder", "getClassesEnumsFromFolder", "folderNode", "collectChildNodes", "deleteDiagram", "nodeExistOrNot", "some", "findCurrentDiagramParentNode", "currentDiagramTag", "cloned", "_", "i0", "ɵɵinject", "i1", "DataFormatService", "i2", "ClassService", "i3", "EnumerationService", "i4", "FolderService", "i5", "DiagramUtils", "_2", "factory", "ɵfac", "providedIn"], "sources": ["D:\\GitHub\\Bassetti\\devint-BASSETTI-GROUP-APP\\BassettiUMLWebApp\\UMLApp\\src\\app\\core\\services\\treeNode\\tree-node.service.ts"], "sourcesContent": ["import { computed, Injectable, signal } from '@angular/core';\r\nimport { Observable, Subject } from 'rxjs';\r\nimport { AttributeMemberType } from 'src/app/shared/model/attribute';\r\nimport { FolderDTO, TemplateClass } from 'src/app/shared/model/class';\r\nimport { GoJsNodeIcon } from 'src/app/shared/model/common';\r\nimport { Diagram } from 'src/app/shared/model/diagram';\r\nimport { TemplateEnumeration } from 'src/app/shared/model/enumeration';\r\nimport {\r\n  GojsDiagramAttributeNode,\r\n  GojsDiagramClassNode,\r\n  GojsDiagramEnumerationNode,\r\n  GojsDiagramLiteralNode,\r\n  GojsFolderNode,\r\n  GojsNodeCategory,\r\n} from 'src/app/shared/model/gojs';\r\nimport { ProjectDetails } from 'src/app/shared/model/project';\r\nimport { TreeNode, TreeNodeTag } from 'src/app/shared/model/treeNode';\r\nimport {\r\n  ClassWrapperCategory,\r\n  DiagramWrapperCategory,\r\n  DiagramWrapperName,\r\n  EnumWrapperCategory,\r\n} from 'src/app/shared/utils/constants';\r\nimport { DiagramUtils } from 'src/app/shared/utils/diagram-utils';\r\nimport { ClassService } from '../class/class.service';\r\nimport { DataFormatService } from '../data-format/data-format.service';\r\nimport { EnumerationService } from '../enumeration/enumeration.service';\r\nimport { FolderService } from '../folder/folder.service';\r\n\r\n@Injectable({\r\n  providedIn: 'root',\r\n})\r\nexport class TreeNodeService {\r\n  // Signal for library details\r\n  private libraryDetailsSignal = signal<TreeNode | null>(null);\r\n\r\n  // Subject for backward compatibility\r\n  private libraryDetailsSubject = new Subject<TreeNode | null>();\r\n\r\n  // Computed signal for descendant nodes\r\n  readonly descendantTreeNodes = computed(() => {\r\n    const libraryDetails = this.libraryDetailsSignal();\r\n    if (!libraryDetails) return [];\r\n    return this.getDescendantNodes(libraryDetails);\r\n  });\r\n\r\n  private currentDiagramId: number = -1;\r\n\r\n  CATEGORY_PRIORITY = [\r\n    DiagramWrapperCategory,\r\n    ClassWrapperCategory,\r\n    EnumWrapperCategory,\r\n    GojsNodeCategory.Folder, // Always at the end\r\n  ];\r\n\r\n  constructor(\r\n    private dataFormatService: DataFormatService,\r\n    private _classService: ClassService,\r\n    private _enumerationService: EnumerationService,\r\n    private _folderService: FolderService,\r\n    private _diagramUtils: DiagramUtils\r\n  ) {\r\n    this._diagramUtils.activeDiagramChanges().subscribe((diagram) => {\r\n      if (diagram && diagram.id) this.currentDiagramId = diagram.id;\r\n    });\r\n  }\r\n\r\n  getLibraryDetails(): Observable<TreeNode | null> {\r\n    return this.libraryDetailsSubject.asObservable();\r\n  }\r\n\r\n  setLibraryDetails(projectDetails: ProjectDetails) {\r\n    const formattedNode = this.formatTreeData(projectDetails);\r\n    this.libraryDetailsSignal.set(formattedNode);\r\n    this.libraryDetailsSubject.next(formattedNode);\r\n  }\r\n\r\n  private getDescendantNodes(node: TreeNode): TreeNode[] {\r\n    const descendants: TreeNode[] = [];\r\n    const traverse = (currentNode: TreeNode) => {\r\n      if (currentNode.children) {\r\n        currentNode.children.forEach((child) => {\r\n          descendants.push(child);\r\n          traverse(child);\r\n        });\r\n      }\r\n    };\r\n    traverse(node);\r\n    return descendants;\r\n  }\r\n\r\n  getWrapperParentTag(wrapperTag: string) {\r\n    return `${wrapperTag}_${TreeNodeTag.Project}`;\r\n  }\r\n\r\n  formatTreeData(projectDetails: ProjectDetails): TreeNode {\r\n    const treeNode = {\r\n      name: projectDetails.name,\r\n      category: GojsNodeCategory.Project,\r\n      children: [\r\n        ...(projectDetails.diagrams.length > 0\r\n          ? [\r\n              this.formatDiagramTreeNode(\r\n                projectDetails.diagrams,\r\n                TreeNodeTag.Project,\r\n                projectDetails.id!\r\n              ),\r\n            ]\r\n          : []),\r\n        ...this.formatClassAndEnum(\r\n          projectDetails.templateClasses,\r\n          projectDetails.templateEnumerations,\r\n          TreeNodeTag.Project\r\n        ),\r\n        ...this.formatFoldersRecursively(\r\n          projectDetails.folders,\r\n          TreeNodeTag.Project,\r\n          projectDetails.id!\r\n        ),\r\n      ],\r\n      tag: TreeNodeTag.Project,\r\n      icon: GoJsNodeIcon.Project,\r\n      supportingNodes: [\r\n        GojsNodeCategory.Class,\r\n        GojsNodeCategory.AssociativeClass,\r\n        GojsNodeCategory.Enumeration,\r\n        GojsNodeCategory.Folder,\r\n        GojsNodeCategory.Diagram,\r\n      ],\r\n    };\r\n    return treeNode;\r\n  }\r\n\r\n  private formatFoldersRecursively(\r\n    folders: FolderDTO[],\r\n    parentTag: string,\r\n    projectId: number\r\n  ): TreeNode[] {\r\n    return folders.map((folder) => {\r\n      const children: TreeNode[] = [\r\n        ...this.formatClassAndEnum(\r\n          folder.templateClasses!,\r\n          folder.templateEnumerations!,\r\n          `atTag${GojsNodeCategory.Folder}_${folder.id}`\r\n        ),\r\n        ...this.sortTreeNodeChildren(\r\n          this.formatFoldersRecursively(\r\n            folder.childFolders || [],\r\n            `atTag${GojsNodeCategory.Folder}_${folder.id}`,\r\n            projectId\r\n          )\r\n        ),\r\n      ];\r\n\r\n      // Add diagrams to children only if their length is greater than zero\r\n      if (folder.diagrams.length > 0) {\r\n        children.unshift(\r\n          this.formatDiagramTreeNode(\r\n            folder.diagrams,\r\n            `atTag${GojsNodeCategory.Folder}_${folder.id}`,\r\n            projectId\r\n          )\r\n        );\r\n      }\r\n\r\n      return {\r\n        name: folder.name,\r\n        children: children,\r\n        category: GojsNodeCategory.Folder,\r\n        icon: GoJsNodeIcon.Folder,\r\n        data: this.dataFormatService.formatFolderData(folder),\r\n        tag: `atTag${GojsNodeCategory.Folder}_${folder.id}`,\r\n        parentTag: parentTag,\r\n        isDraggable: true,\r\n        supportingNodes: [\r\n          GojsNodeCategory.Class,\r\n          GojsNodeCategory.AssociativeClass,\r\n          GojsNodeCategory.Enumeration,\r\n          GojsNodeCategory.Folder,\r\n          GojsNodeCategory.Diagram,\r\n        ],\r\n      };\r\n    });\r\n  }\r\n\r\n  private formatClassAndEnum(\r\n    templateClasses: TemplateClass[],\r\n    templateEnumerations: TemplateEnumeration[],\r\n    parentTag: string\r\n  ): TreeNode[] {\r\n    const treeNodes: TreeNode[] = [];\r\n    if (templateClasses.length > 0) {\r\n      treeNodes.push({\r\n        name: 'Classes',\r\n        children: this.sortTreeNodeChildren(\r\n          templateClasses.map((tempClass) => ({\r\n            name: tempClass.name,\r\n            children: this.sortTreeNodeChildren(\r\n              this.formatAttributeNode(tempClass)\r\n            ),\r\n            category: tempClass.isAssociative\r\n              ? GojsNodeCategory.AssociativeClass\r\n              : GojsNodeCategory.Class,\r\n            tag: `atTag${GojsNodeCategory.Class}_${tempClass.id}`,\r\n            icon: tempClass.isAssociative\r\n              ? GoJsNodeIcon.Associative\r\n              : GoJsNodeIcon.Class,\r\n            parentTag: `${TreeNodeTag.ClassWrapper}_${parentTag}`,\r\n            data: this.dataFormatService.formatDiagramClassNode(\r\n              tempClass,\r\n              this.dataFormatService.formatAttributeData(\r\n                tempClass.attributes || []\r\n              )\r\n            ),\r\n            isDraggable: true,\r\n            supportingNodes: [\r\n              GojsNodeCategory.Operation,\r\n              GojsNodeCategory.Attribute,\r\n            ],\r\n          }))\r\n        ),\r\n        supportingNodes: [\r\n          GojsNodeCategory.Class,\r\n          GojsNodeCategory.AssociativeClass,\r\n        ],\r\n        category: ClassWrapperCategory,\r\n        tag: `${TreeNodeTag.ClassWrapper}_${parentTag}`,\r\n        parentTag: parentTag,\r\n        icon: GoJsNodeIcon.Class,\r\n      });\r\n    }\r\n    if (templateEnumerations.length > 0) {\r\n      treeNodes.push({\r\n        name: 'Enumerations',\r\n        children: this.sortTreeNodeChildren(\r\n          this.getTemplateEnumsForTree(templateEnumerations, parentTag)\r\n        ),\r\n        category: EnumWrapperCategory,\r\n        icon: GoJsNodeIcon.Enumeration,\r\n        tag: `${TreeNodeTag.EnumerationWrapper}_${parentTag}`,\r\n        parentTag: parentTag,\r\n        supportingNodes: [GojsNodeCategory.Enumeration],\r\n      });\r\n    }\r\n    return treeNodes;\r\n  }\r\n\r\n  private formatDiagramTreeNode(\r\n    diagrams: Diagram[],\r\n    parentTag: string,\r\n    projectId: number\r\n  ): TreeNode {\r\n    return {\r\n      name: DiagramWrapperName,\r\n      children: diagrams.map((diagram) => ({\r\n        name: diagram.name,\r\n        children: [],\r\n        category: GojsNodeCategory.Diagram,\r\n        icon: GoJsNodeIcon.Diagram,\r\n        tag: `atTag${GojsNodeCategory.Diagram}_${diagram.id}`,\r\n        data: { ...diagram, idProject: projectId },\r\n        parentTag: `${TreeNodeTag.DiagramWrapper}_${parentTag}`,\r\n        isDraggable: true,\r\n      })),\r\n      tag: `${TreeNodeTag.DiagramWrapper}_${parentTag}`,\r\n      parentTag: parentTag,\r\n      category: DiagramWrapperCategory,\r\n      icon: GoJsNodeIcon.Diagram,\r\n      supportingNodes: [GojsNodeCategory.Diagram],\r\n    };\r\n  }\r\n\r\n  private getTemplateEnumsForTree(\r\n    templateEnumerations: TemplateEnumeration[],\r\n    parentTag: string\r\n  ): TreeNode[] {\r\n    return templateEnumerations.map((tempEnum) => {\r\n      // add AttributeTypes for each tempEnum\r\n      this._diagramUtils.addAttributeTypes({\r\n        id: tempEnum.id?.toString()!,\r\n        name: tempEnum?.name,\r\n        isEnumeration: true,\r\n      });\r\n\r\n      // Return the formatted tree node for each tempEnum\r\n      return {\r\n        name: tempEnum.name,\r\n        children: this.formatLiteralTreeNode(tempEnum),\r\n        category: GojsNodeCategory.Enumeration,\r\n        tag: `atTag${GojsNodeCategory.Enumeration}_${tempEnum.id}`,\r\n        parentTag: `${TreeNodeTag.EnumerationWrapper}_${parentTag}`,\r\n        icon: GoJsNodeIcon.Enumeration,\r\n        data: this.dataFormatService.formatDiagramEnumData(\r\n          tempEnum,\r\n          this.dataFormatService.formatLiteralData(\r\n            tempEnum.enumerationLiterals || []\r\n          )\r\n        ),\r\n        isDraggable: true,\r\n        supportingNodes: [GojsNodeCategory.EnumerationLiteral],\r\n      };\r\n    });\r\n  }\r\n\r\n  private formatLiteralTreeNode(tempEnum: TemplateEnumeration): TreeNode[] {\r\n    return (\r\n      tempEnum.enumerationLiterals?.map((literal) => ({\r\n        name: literal.name,\r\n        children: [],\r\n        category: GojsNodeCategory.EnumerationLiteral,\r\n        icon: GoJsNodeIcon.EnumerationLiteral,\r\n        tag: `atTag${GojsNodeCategory.EnumerationLiteral}_${literal.id}`,\r\n        parentTag: `atTag${GojsNodeCategory.Enumeration}_${tempEnum.id}`,\r\n        supportingNodes: [],\r\n        data: {\r\n          ...this.dataFormatService.formatLiteralData([literal])[0],\r\n          idTemplateEnumeration: tempEnum.id,\r\n        },\r\n      })) || []\r\n    );\r\n  }\r\n\r\n  private formatAttributeNode(tempClass: TemplateClass): TreeNode[] {\r\n    return tempClass.attributes.map((attr) => ({\r\n      name: attr.name,\r\n      children: [],\r\n      category:\r\n        attr.category == AttributeMemberType.attribute\r\n          ? GojsNodeCategory.Attribute\r\n          : GojsNodeCategory.Operation,\r\n      icon:\r\n        attr.category == AttributeMemberType.attribute\r\n          ? GoJsNodeIcon.Attribute\r\n          : GoJsNodeIcon.Operation,\r\n      tag: `atTag${\r\n        attr.category == AttributeMemberType.attribute\r\n          ? GojsNodeCategory.Attribute\r\n          : GojsNodeCategory.Operation\r\n      }_${attr.id}`,\r\n      parentTag: `atTag${GojsNodeCategory.Class}_${tempClass.id}`,\r\n      data: {\r\n        ...this.dataFormatService.formatAttributeData([attr])[0],\r\n        idTemplateClass: tempClass.id,\r\n      },\r\n      supportingNodes: [],\r\n    }));\r\n  }\r\n\r\n  /**\r\n   * Adds a group node to the tree structure by either appending it to a parent node\r\n   * or creating a wrapper node if a suitable parent is not found. The method handles\r\n   * nodes based on their category, organizing `Folder` nodes separately from other types.\r\n   * After insertion, the tree nodes are sorted to maintain a defined order.\r\n   * @param nodeData - The data for the node to be added to the tree.\r\n   *                   It contains the node's details, such as category and parent tag.\r\n   */\r\n  addGroupNodeInTree(nodeData: TreeNode): void {\r\n    const treeNode = this.libraryDetailsSignal();\r\n    if (!treeNode) return;\r\n\r\n    const updatedTreeNode = { ...treeNode };\r\n    const isProjectOrFolderNode =\r\n      nodeData.category === GojsNodeCategory.Folder &&\r\n      nodeData.parentTag === TreeNodeTag.Project;\r\n\r\n    if (isProjectOrFolderNode) {\r\n      this.addNodeToChildren(nodeData, updatedTreeNode.children);\r\n    } else {\r\n      const parentNode = this.findNodeByTag(nodeData.parentTag!);\r\n      if (parentNode) {\r\n        this.addNodeToParent(nodeData, parentNode);\r\n      } else {\r\n        // If parent not found, add to root\r\n        this.getOrCreateWrapperNode(nodeData, updatedTreeNode);\r\n      }\r\n    }\r\n\r\n    // Ensure the tree structure is sorted and updated\r\n    updatedTreeNode.children = this.sortTreeNodes(updatedTreeNode.children);\r\n    this.libraryDetailsSignal.set(updatedTreeNode);\r\n    this.libraryDetailsSubject.next(updatedTreeNode);\r\n  }\r\n\r\n  private addNodeToChildren(node: TreeNode, children: TreeNode[]): void {\r\n    children.push(node);\r\n    this.sortTreeNodeChildren(children);\r\n  }\r\n\r\n  private addNodeToParent(node: TreeNode, parentNode: TreeNode): void {\r\n    if (parentNode.category === GojsNodeCategory.Folder) {\r\n      if (node.category !== GojsNodeCategory.Folder) {\r\n        this.getOrCreateWrapperNode(node, parentNode);\r\n      } else {\r\n        this.addNodeToChildren(node, parentNode.children);\r\n      }\r\n    } else {\r\n      this.addNodeToChildren(node, parentNode.children);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Sorts an array of tree nodes based on the priority of their categories.\r\n   * The priority order is defined in `CATEGORY_PRIORITY`. Nodes with undefined\r\n   * categories are placed at the end of the list.\r\n   *\r\n   * @param nodes - An array of `TreeNode` objects to be sorted.\r\n   * @returns A sorted array of `TreeNode` objects, ordered by their category priority.\r\n   */\r\n  private sortTreeNodes(nodes: TreeNode[]): TreeNode[] {\r\n    return nodes.sort((a, b) => {\r\n      // Get the priority index of each node's category\r\n      const indexA = this.CATEGORY_PRIORITY.indexOf(a.category);\r\n      const indexB = this.CATEGORY_PRIORITY.indexOf(b.category);\r\n\r\n      // Place nodes with unknown categories at the end\r\n      if (indexA === -1) return 1;\r\n      if (indexB === -1) return -1;\r\n\r\n      // Sort by priority index in ascending order\r\n      return indexA - indexB;\r\n    });\r\n  }\r\n\r\n  private sortTreeNodeChildren(nodes: TreeNode[]): TreeNode[] {\r\n    return nodes.sort((a, b) =>\r\n      a.name.localeCompare(b.name, undefined, { sensitivity: 'base' })\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Ensures that a wrapper node exists for the specified `targetedNode` within the `parentNode`.\r\n   * If the wrapper node already exists, the `targetedNode` is added to its children.\r\n   * Otherwise, a new wrapper node is created, added to the `parentNode`, and the `targetedNode`\r\n   * is added as its child.\r\n   *\r\n   * @param targetedNode - The `TreeNode` that needs to be added to a wrapper node.\r\n   * @param parentNode - The parent `TreeNode` under which the wrapper node will be created or updated.\r\n   */\r\n  private getOrCreateWrapperNode(\r\n    targetedNode: TreeNode,\r\n    parentNode: TreeNode\r\n  ): void {\r\n    // Attempt to find an existing wrapper node within the parent's children\r\n    let wrapperNode = parentNode.children.find(\r\n      (node) =>\r\n        node.tag ==\r\n        `${\r\n          targetedNode.category == GojsNodeCategory.Class ||\r\n          targetedNode.category == GojsNodeCategory.AssociativeClass\r\n            ? TreeNodeTag.ClassWrapper\r\n            : targetedNode.category === GojsNodeCategory.Diagram\r\n            ? TreeNodeTag.DiagramWrapper\r\n            : TreeNodeTag.EnumerationWrapper\r\n        }_${targetedNode.parentTag}`\r\n    );\r\n\r\n    if (wrapperNode) {\r\n      // If the wrapper node exists, add the targeted node as its child\r\n      wrapperNode.children.push(targetedNode);\r\n      this.sortTreeNodeChildren(wrapperNode.children);\r\n    } else {\r\n      // Create a new wrapper node if it doesn't exist\r\n      wrapperNode = this.constructWrapperNode(targetedNode, parentNode.tag);\r\n      parentNode?.children.push({\r\n        ...wrapperNode,\r\n        children: this.sortTreeNodeChildren([\r\n          ...wrapperNode.children,\r\n          targetedNode,\r\n        ]),\r\n      });\r\n      this.sortTreeNodes(parentNode.children);\r\n    }\r\n  }\r\n\r\n  addItemNodeInParent(itemNode: TreeNode) {\r\n    const treeNode = this.libraryDetailsSignal();\r\n    if (!treeNode) return;\r\n\r\n    const parentNode = this.findNodeByTag(itemNode.parentTag!);\r\n    if (parentNode) {\r\n      (\r\n        parentNode.data as GojsDiagramClassNode | GojsDiagramEnumerationNode\r\n      ).items.push(\r\n        itemNode.data as GojsDiagramAttributeNode | GojsDiagramLiteralNode\r\n      );\r\n      parentNode.children.push(itemNode);\r\n      this.sortTreeNodeChildren(parentNode.children);\r\n\r\n      // Create a new tree instance to trigger change detection\r\n      const updatedTree = this.cloneTreeNode(treeNode);\r\n      this.libraryDetailsSignal.set(updatedTree);\r\n      this.libraryDetailsSubject.next(updatedTree);\r\n    }\r\n  }\r\n\r\n  editGroupTreeNode(treeNode: TreeNode) {\r\n    const updatedLibraryDetails = this.libraryDetailsSignal();\r\n    if (!updatedLibraryDetails) return;\r\n\r\n    const clonedTree = this.cloneTreeNode(updatedLibraryDetails);\r\n    const parentNode = this.findParentNode(treeNode.tag, clonedTree);\r\n\r\n    if (parentNode) {\r\n      const nodeToUpdate = parentNode?.children.find(\r\n        (node) => node.tag === treeNode.tag\r\n      );\r\n      if (nodeToUpdate && nodeToUpdate.data && treeNode.data) {\r\n        nodeToUpdate.name = treeNode.data.name;\r\n        if (\r\n          nodeToUpdate.category === GojsNodeCategory.Attribute ||\r\n          nodeToUpdate.category === GojsNodeCategory.Operation ||\r\n          nodeToUpdate.category === GojsNodeCategory.EnumerationLiteral\r\n        ) {\r\n          this.updateItemInClassOrEnum(parentNode, treeNode, clonedTree);\r\n          if (\r\n            nodeToUpdate.category == GojsNodeCategory.Attribute ||\r\n            nodeToUpdate.category === GojsNodeCategory.Operation\r\n          ) {\r\n            const itemNode = nodeToUpdate.data as GojsDiagramAttributeNode;\r\n            this.updateNodeData(nodeToUpdate.data, {\r\n              name: itemNode.name,\r\n              id: itemNode.id,\r\n              description: itemNode.description,\r\n              dataType: itemNode.dataType,\r\n            });\r\n          } else {\r\n            const itemNode = nodeToUpdate.data as GojsDiagramLiteralNode;\r\n            this.updateNodeData(nodeToUpdate.data, {\r\n              name: itemNode.name,\r\n              id: itemNode.id,\r\n            });\r\n          }\r\n        } else {\r\n          const classOrEnumNode = treeNode.data as\r\n            | GojsDiagramClassNode\r\n            | GojsDiagramEnumerationNode;\r\n          this.updateNodeData(nodeToUpdate.data, {\r\n            name: classOrEnumNode.name,\r\n            id: classOrEnumNode.id,\r\n            color: classOrEnumNode.color,\r\n            description: classOrEnumNode.description,\r\n            tag: classOrEnumNode.tag,\r\n            volumetry: classOrEnumNode.volumetry,\r\n            treeNodeTag: classOrEnumNode.treeNodeTag,\r\n            position: classOrEnumNode.position,\r\n            size: classOrEnumNode.size,\r\n          });\r\n        }\r\n        this.sortTreeNodeChildren(parentNode?.children);\r\n        this.sortTreeNodes(parentNode?.children);\r\n        this.libraryDetailsSignal.set(clonedTree);\r\n        this.libraryDetailsSubject.next(clonedTree);\r\n      }\r\n    }\r\n  }\r\n\r\n  private updateNodeData<T extends object>(\r\n    nodeToUpdate: T,\r\n    treeNodeData: Partial<T>\r\n  ): void {\r\n    Object.keys(treeNodeData).forEach((key) => {\r\n      if (key in nodeToUpdate) {\r\n        (nodeToUpdate as any)[key] = (treeNodeData as any)[key];\r\n      }\r\n    });\r\n  }\r\n\r\n  updateItemInClassOrEnum(\r\n    groupNode: TreeNode,\r\n    treeNode: TreeNode,\r\n    libraryDetails: TreeNode\r\n  ) {\r\n    if (groupNode.data && treeNode.data) {\r\n      (\r\n        groupNode.data as GojsDiagramClassNode | GojsDiagramEnumerationNode\r\n      ).items.forEach((item) => {\r\n        if (item.id == treeNode.data?.id) {\r\n          Object.assign(item, treeNode.data);\r\n        }\r\n      });\r\n      this.libraryDetailsSignal.set(libraryDetails);\r\n      this.libraryDetailsSubject.next(libraryDetails);\r\n    }\r\n  }\r\n\r\n  deleteGroupTreeNode(treeNode: TreeNode) {\r\n    const updatedLibraryDetails = this.libraryDetailsSignal();\r\n    if (!updatedLibraryDetails) return;\r\n\r\n    const clonedTree = this.cloneTreeNode(updatedLibraryDetails);\r\n    const parentNode = this.findParentNode(treeNode.tag, clonedTree);\r\n\r\n    if (parentNode) {\r\n      const index = parentNode?.children.findIndex(\r\n        (node) => node.tag === treeNode.tag\r\n      );\r\n      if (\r\n        treeNode.category === GojsNodeCategory.Attribute ||\r\n        treeNode.category === GojsNodeCategory.Operation ||\r\n        treeNode.category === GojsNodeCategory.EnumerationLiteral\r\n      ) {\r\n        (\r\n          parentNode.data as GojsDiagramClassNode | GojsDiagramEnumerationNode\r\n        ).items = (\r\n          parentNode.data as GojsDiagramClassNode | GojsDiagramEnumerationNode\r\n        ).items.filter((item) => item.id !== treeNode.data?.id);\r\n      }\r\n      if (index > -1) {\r\n        parentNode?.children.splice(index, 1);\r\n        if (\r\n          parentNode.children.length == 0 &&\r\n          (parentNode.category == ClassWrapperCategory ||\r\n            parentNode.category == EnumWrapperCategory ||\r\n            parentNode.category == DiagramWrapperCategory)\r\n        ) {\r\n          const emptyWrapperParentNode = this.findParentNode(\r\n            parentNode.tag,\r\n            clonedTree\r\n          );\r\n          if (emptyWrapperParentNode) {\r\n            emptyWrapperParentNode.children =\r\n              emptyWrapperParentNode.children.filter(\r\n                (child: TreeNode) => child.tag !== parentNode.tag\r\n              );\r\n          }\r\n        }\r\n        this.libraryDetailsSignal.set(clonedTree);\r\n        this.libraryDetailsSubject.next(clonedTree);\r\n      }\r\n    }\r\n  }\r\n\r\n  moveNode(targetFolder: TreeNode, draggedNode: TreeNode) {\r\n    // Get the current value of library details\r\n    const updatedLibraryDetails = this.libraryDetailsSignal();\r\n    if (!updatedLibraryDetails) return;\r\n\r\n    const clonedTree = this.cloneTreeNode(updatedLibraryDetails);\r\n\r\n    // Find and remove the node from its current parent's children array\r\n    const parentNode = this.findParentNode(draggedNode.tag, clonedTree);\r\n\r\n    if (\r\n      !this.checkDropValidation(\r\n        targetFolder,\r\n        draggedNode,\r\n        parentNode,\r\n        clonedTree\r\n      )\r\n    )\r\n      return;\r\n\r\n    if (parentNode) {\r\n      parentNode.children = parentNode.children.filter(\r\n        (child: TreeNode) => child.tag !== draggedNode.tag\r\n      );\r\n      if (\r\n        parentNode.children.length == 0 &&\r\n        (parentNode.category == ClassWrapperCategory ||\r\n          parentNode.category == EnumWrapperCategory ||\r\n          parentNode.category == DiagramWrapperCategory)\r\n      ) {\r\n        const emptyWrapperParentNode = this.findParentNode(\r\n          parentNode.tag,\r\n          clonedTree\r\n        );\r\n        if (emptyWrapperParentNode) {\r\n          emptyWrapperParentNode.children =\r\n            emptyWrapperParentNode.children.filter(\r\n              (child: TreeNode) => child.tag !== parentNode.tag\r\n            );\r\n        }\r\n      }\r\n    }\r\n\r\n    // Add the node to the target folder's children array\r\n    if (\r\n      draggedNode.category === GojsNodeCategory.Folder ||\r\n      targetFolder.category == ClassWrapperCategory ||\r\n      targetFolder.category == EnumWrapperCategory ||\r\n      targetFolder.category == DiagramWrapperCategory\r\n    ) {\r\n      targetFolder.children.push({\r\n        ...draggedNode,\r\n        parentTag: targetFolder.tag,\r\n      });\r\n      this.sortTreeNodeChildren(targetFolder.children);\r\n      this.sortTreeNodes(targetFolder.children);\r\n      this.moveNodeToFolder(targetFolder, draggedNode);\r\n    } else {\r\n      const targetFolderNode = this.findNodeByTagInTree(\r\n        targetFolder.tag,\r\n        clonedTree\r\n      );\r\n      if (targetFolderNode) {\r\n        const wrapperNode = this.constructWrapperNode(\r\n          draggedNode,\r\n          targetFolderNode.tag\r\n        );\r\n        const targetedWrapperNode = targetFolderNode?.children.find(\r\n          (node: TreeNode) => node.tag === wrapperNode.tag\r\n        );\r\n        this.moveNodeToFolder(targetFolderNode, draggedNode);\r\n        if (targetedWrapperNode) {\r\n          targetedWrapperNode.children.push({\r\n            ...draggedNode,\r\n            parentTag: targetedWrapperNode.tag,\r\n          });\r\n\r\n          this.sortTreeNodeChildren(targetedWrapperNode.children);\r\n        } else {\r\n          targetFolderNode.children.push({\r\n            ...wrapperNode,\r\n            children: [\r\n              ...wrapperNode.children,\r\n              { ...draggedNode, parentTag: wrapperNode.tag },\r\n            ],\r\n            parentTag: targetFolder.tag,\r\n          });\r\n          this.sortTreeNodes(targetFolderNode.children);\r\n        }\r\n      }\r\n    }\r\n\r\n    // Update the Subject with the modified library details\r\n    this.libraryDetailsSignal.set(clonedTree);\r\n    this.libraryDetailsSubject.next(clonedTree);\r\n  }\r\n\r\n  private checkDropValidation(\r\n    targetNode: TreeNode,\r\n    draggedNode: TreeNode,\r\n    parentNode: TreeNode | null,\r\n    libraryDetails: TreeNode\r\n  ): boolean {\r\n    if (\r\n      parentNode?.parentTag === libraryDetails?.tag &&\r\n      targetNode.category === GojsNodeCategory.Project &&\r\n      draggedNode.category !== GojsNodeCategory.Folder\r\n    ) {\r\n      return false;\r\n    }\r\n    if (\r\n      draggedNode.tag === targetNode.tag ||\r\n      draggedNode.tag == targetNode.parentTag ||\r\n      draggedNode.parentTag == targetNode.tag\r\n    )\r\n      return false;\r\n\r\n    if (targetNode.category === GojsNodeCategory.Diagram) return false;\r\n\r\n    // Check if the current parent is the dragged node\r\n    let currentParent = this.findParentNode(targetNode.tag, libraryDetails);\r\n    if (\r\n      (targetNode.category == ClassWrapperCategory ||\r\n        targetNode.category == DiagramWrapperCategory ||\r\n        targetNode.category == EnumWrapperCategory) &&\r\n      currentParent?.category == GojsNodeCategory.Folder\r\n    ) {\r\n      return false;\r\n    }\r\n    while (currentParent) {\r\n      if (currentParent.tag === draggedNode.tag) {\r\n        return false; // Found an ancestor\r\n      }\r\n      currentParent = this.findParentNode(currentParent.tag, libraryDetails);\r\n    }\r\n    return true;\r\n  }\r\n\r\n  findParentNode(nodeTag: string, folder: TreeNode): TreeNode | null {\r\n    if (folder.children) {\r\n      for (let child of folder.children) {\r\n        if (child.tag === nodeTag) {\r\n          return folder;\r\n        }\r\n        const node = this.findParentNode(nodeTag, child);\r\n        if (node) return node;\r\n      }\r\n    }\r\n    return null;\r\n  }\r\n\r\n  findNodeByTag(tag: string): TreeNode | null {\r\n    if (tag == TreeNodeTag.Project) {\r\n      return this.libraryDetailsSignal();\r\n    }\r\n    const descendants = this.descendantTreeNodes();\r\n    return descendants.find((node) => node.tag == tag) || null;\r\n  }\r\n\r\n  private findNodeByTagInTree(tag: string, tree: TreeNode): TreeNode | null {\r\n    if (tree.tag === tag) {\r\n      return tree;\r\n    }\r\n    if (tree.children) {\r\n      for (const child of tree.children) {\r\n        const found = this.findNodeByTagInTree(tag, child);\r\n        if (found) return found;\r\n      }\r\n    }\r\n    return null;\r\n  }\r\n\r\n  private constructWrapperNode(\r\n    draggedNode: TreeNode,\r\n    parentTag: string\r\n  ): TreeNode {\r\n    const wrapperNodeName =\r\n      draggedNode.category === GojsNodeCategory.Class ||\r\n      draggedNode.category === GojsNodeCategory.AssociativeClass\r\n        ? 'Classes'\r\n        : draggedNode.category === GojsNodeCategory.Diagram\r\n        ? 'Diagrams'\r\n        : 'Enumerations';\r\n    const wrapperNodeTag =\r\n      draggedNode.category === GojsNodeCategory.Class ||\r\n      draggedNode.category === GojsNodeCategory.AssociativeClass\r\n        ? TreeNodeTag.ClassWrapper\r\n        : draggedNode.category === GojsNodeCategory.Diagram\r\n        ? TreeNodeTag.DiagramWrapper\r\n        : TreeNodeTag.EnumerationWrapper;\r\n    const wrapperNodeCategory =\r\n      draggedNode.category === GojsNodeCategory.Class ||\r\n      draggedNode.category === GojsNodeCategory.AssociativeClass\r\n        ? ClassWrapperCategory\r\n        : draggedNode.category === GojsNodeCategory.Diagram\r\n        ? DiagramWrapperCategory\r\n        : EnumWrapperCategory;\r\n    const wrapperNodeIcon =\r\n      draggedNode.category === GojsNodeCategory.Class ||\r\n      draggedNode.category === GojsNodeCategory.AssociativeClass\r\n        ? GoJsNodeIcon.Class\r\n        : draggedNode.category === GojsNodeCategory.Diagram\r\n        ? GoJsNodeIcon.Diagram\r\n        : GoJsNodeIcon.Enumeration;\r\n    return {\r\n      name: wrapperNodeName,\r\n      children: [],\r\n      category: wrapperNodeCategory,\r\n      tag: `${wrapperNodeTag}_${parentTag}`,\r\n      parentTag: parentTag,\r\n      icon: wrapperNodeIcon,\r\n      supportingNodes: [draggedNode.category],\r\n    };\r\n  }\r\n\r\n  private moveNodeToFolder(targetNode: TreeNode, draggedNode: TreeNode) {\r\n    if (draggedNode.data) {\r\n      if (targetNode.category === GojsNodeCategory.Folder) {\r\n        if (draggedNode.category === GojsNodeCategory.Class) {\r\n          this._classService\r\n            .moveTempClassToFolder({\r\n              id: (draggedNode?.data as GojsDiagramClassNode).idTemplateClass,\r\n              idFolder: (targetNode?.data as GojsFolderNode).idFolder!,\r\n            })\r\n            .subscribe();\r\n        } else if (draggedNode.category === GojsNodeCategory.Enumeration) {\r\n          this._enumerationService\r\n            .moveTempEnumToFolder({\r\n              id: (draggedNode?.data as GojsDiagramEnumerationNode)\r\n                .idTemplateEnumeration,\r\n              idFolder: (targetNode?.data as GojsFolderNode).idFolder!,\r\n            })\r\n            .subscribe();\r\n        } else if (draggedNode.category === GojsNodeCategory.Diagram) {\r\n          this._folderService.moveDiagramToFolder({\r\n            id: (draggedNode?.data as Diagram).id!,\r\n            idFolder: (targetNode?.data as GojsFolderNode).idFolder!,\r\n          });\r\n        } else if (draggedNode.category === GojsNodeCategory.Folder) {\r\n          this._folderService.moveFolderToFolder({\r\n            id: (draggedNode?.data as GojsFolderNode).idFolder!,\r\n            parentFolderId: (targetNode?.data as GojsFolderNode).idFolder!,\r\n          });\r\n        }\r\n      } else {\r\n        if (draggedNode.category === GojsNodeCategory.Class) {\r\n          this._classService\r\n            .removeTempClassFromFolder(\r\n              (draggedNode?.data as GojsDiagramClassNode).idTemplateClass\r\n            )\r\n            .subscribe();\r\n        } else if (draggedNode.category === GojsNodeCategory.Enumeration) {\r\n          this._enumerationService\r\n            .removeTempEnumFromFolder(\r\n              (draggedNode?.data as GojsDiagramEnumerationNode)\r\n                .idTemplateEnumeration\r\n            )\r\n            .subscribe();\r\n        } else if (draggedNode.category === GojsNodeCategory.Diagram) {\r\n          this._folderService.removeDiagramFromFolder(\r\n            (draggedNode?.data as Diagram).id!\r\n          );\r\n        } else if (draggedNode.category === GojsNodeCategory.Folder) {\r\n          this._folderService.removeFolderFromFolder(\r\n            (draggedNode?.data as GojsFolderNode).idFolder!\r\n          );\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  getClassesEnumsFromFolder(folderNode: TreeNode): TreeNode[] {\r\n    const nodes: TreeNode[] = [];\r\n    // Recursive function to collect classes, enums, and folders from the target folder\r\n    const collectChildNodes = (node: TreeNode) => {\r\n      node.children.forEach((child) => {\r\n        if (\r\n          child.category === ClassWrapperCategory ||\r\n          child.category === EnumWrapperCategory\r\n        ) {\r\n          nodes.push(...child.children);\r\n        } else if (child.category === GojsNodeCategory.Folder) {\r\n          collectChildNodes(child);\r\n        }\r\n      });\r\n    };\r\n    // Start collecting from the folder node\r\n    collectChildNodes(folderNode);\r\n    return nodes;\r\n  }\r\n\r\n  /**\r\n   * Deletes a diagram node by tag.\r\n   * @param {string} tag - Unique identifier for the node.\r\n   * @memberof TreeNodeService\r\n   * @returns {void}\r\n   */\r\n  deleteDiagram(tag: string): void {\r\n    const node = this.findNodeByTag(tag);\r\n    if (node) this.deleteGroupTreeNode(node);\r\n  }\r\n\r\n  nodeExistOrNot(parentTag: string, nodes: TreeNode[]): boolean {\r\n    const libraryDetails = this.libraryDetailsSignal();\r\n    if (libraryDetails) {\r\n      const parentNode = this.findParentNode(parentTag, libraryDetails);\r\n      if (parentNode) {\r\n        if (parentNode.category == GojsNodeCategory.Folder) {\r\n          if (nodes.some((node) => node.tag === parentNode.tag)) return true;\r\n          else return this.nodeExistOrNot(parentNode.tag, nodes);\r\n        }\r\n        return nodes.some((node) => node.tag === parentNode.tag);\r\n      } else return false;\r\n    } else return false;\r\n  }\r\n\r\n  findCurrentDiagramParentNode(): TreeNode | null {\r\n    const currentDiagramTag = `atTag${GojsNodeCategory.Diagram}_${this.currentDiagramId}`;\r\n    const libraryDetails = this.libraryDetailsSignal();\r\n    if (libraryDetails) {\r\n      const wrapperNode = this.findParentNode(\r\n        currentDiagramTag,\r\n        libraryDetails\r\n      );\r\n      if (wrapperNode) {\r\n        return this.findNodeByTag(wrapperNode.parentTag!);\r\n      } else return null;\r\n    } else return null;\r\n  }\r\n\r\n  /**\r\n   * Deep clones a tree node and all its children\r\n   * @param node The node to clone\r\n   * @returns A deep copy of the node\r\n   */\r\n  private cloneTreeNode(node: TreeNode): TreeNode {\r\n    const cloned: TreeNode = {\r\n      ...node,\r\n      children: node.children\r\n        ? node.children.map((child) => this.cloneTreeNode(child))\r\n        : [],\r\n      data: node.data ? { ...node.data } : undefined,\r\n    };\r\n    return cloned;\r\n  }\r\n}\r\n"], "mappings": "AAAA,SAASA,QAAQ,EAAcC,MAAM,QAAQ,eAAe;AAC5D,SAAqBC,OAAO,QAAQ,MAAM;AAC1C,SAASC,mBAAmB,QAAQ,gCAAgC;AAEpE,SAASC,YAAY,QAAQ,6BAA6B;AAG1D,SAMEC,gBAAgB,QACX,2BAA2B;AAElC,SAAmBC,WAAW,QAAQ,+BAA+B;AACrE,SACEC,oBAAoB,EACpBC,sBAAsB,EACtBC,kBAAkB,EAClBC,mBAAmB,QACd,gCAAgC;;;;;;;AAUvC,OAAM,MAAOC,eAAe;EAuB1BC,YACUC,iBAAoC,EACpCC,aAA2B,EAC3BC,mBAAuC,EACvCC,cAA6B,EAC7BC,aAA2B;IAJ3B,KAAAJ,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,mBAAmB,GAAnBA,mBAAmB;IACnB,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,aAAa,GAAbA,aAAa;IA3BvB;IACQ,KAAAC,oBAAoB,GAAGjB,MAAM,CAAkB,IAAI,CAAC;IAE5D;IACQ,KAAAkB,qBAAqB,GAAG,IAAIjB,OAAO,EAAmB;IAE9D;IACS,KAAAkB,mBAAmB,GAAGpB,QAAQ,CAAC,MAAK;MAC3C,MAAMqB,cAAc,GAAG,IAAI,CAACH,oBAAoB,EAAE;MAClD,IAAI,CAACG,cAAc,EAAE,OAAO,EAAE;MAC9B,OAAO,IAAI,CAACC,kBAAkB,CAACD,cAAc,CAAC;IAChD,CAAC,CAAC;IAEM,KAAAE,gBAAgB,GAAW,CAAC,CAAC;IAErC,KAAAC,iBAAiB,GAAG,CAClBhB,sBAAsB,EACtBD,oBAAoB,EACpBG,mBAAmB,EACnBL,gBAAgB,CAACoB,MAAM,CAAE;IAAA,CAC1B;IASC,IAAI,CAACR,aAAa,CAACS,oBAAoB,EAAE,CAACC,SAAS,CAAEC,OAAO,IAAI;MAC9D,IAAIA,OAAO,IAAIA,OAAO,CAACC,EAAE,EAAE,IAAI,CAACN,gBAAgB,GAAGK,OAAO,CAACC,EAAE;IAC/D,CAAC,CAAC;EACJ;EAEAC,iBAAiBA,CAAA;IACf,OAAO,IAAI,CAACX,qBAAqB,CAACY,YAAY,EAAE;EAClD;EAEAC,iBAAiBA,CAACC,cAA8B;IAC9C,MAAMC,aAAa,GAAG,IAAI,CAACC,cAAc,CAACF,cAAc,CAAC;IACzD,IAAI,CAACf,oBAAoB,CAACkB,GAAG,CAACF,aAAa,CAAC;IAC5C,IAAI,CAACf,qBAAqB,CAACkB,IAAI,CAACH,aAAa,CAAC;EAChD;EAEQZ,kBAAkBA,CAACgB,IAAc;IACvC,MAAMC,WAAW,GAAe,EAAE;IAClC,MAAMC,QAAQ,GAAIC,WAAqB,IAAI;MACzC,IAAIA,WAAW,CAACC,QAAQ,EAAE;QACxBD,WAAW,CAACC,QAAQ,CAACC,OAAO,CAAEC,KAAK,IAAI;UACrCL,WAAW,CAACM,IAAI,CAACD,KAAK,CAAC;UACvBJ,QAAQ,CAACI,KAAK,CAAC;QACjB,CAAC,CAAC;;IAEN,CAAC;IACDJ,QAAQ,CAACF,IAAI,CAAC;IACd,OAAOC,WAAW;EACpB;EAEAO,mBAAmBA,CAACC,UAAkB;IACpC,OAAO,GAAGA,UAAU,IAAIzC,WAAW,CAAC0C,OAAO,EAAE;EAC/C;EAEAb,cAAcA,CAACF,cAA8B;IAC3C,MAAMgB,QAAQ,GAAG;MACfC,IAAI,EAAEjB,cAAc,CAACiB,IAAI;MACzBC,QAAQ,EAAE9C,gBAAgB,CAAC2C,OAAO;MAClCN,QAAQ,EAAE,CACR,IAAIT,cAAc,CAACmB,QAAQ,CAACC,MAAM,GAAG,CAAC,GAClC,CACE,IAAI,CAACC,qBAAqB,CACxBrB,cAAc,CAACmB,QAAQ,EACvB9C,WAAW,CAAC0C,OAAO,EACnBf,cAAc,CAACJ,EAAG,CACnB,CACF,GACD,EAAE,CAAC,EACP,GAAG,IAAI,CAAC0B,kBAAkB,CACxBtB,cAAc,CAACuB,eAAe,EAC9BvB,cAAc,CAACwB,oBAAoB,EACnCnD,WAAW,CAAC0C,OAAO,CACpB,EACD,GAAG,IAAI,CAACU,wBAAwB,CAC9BzB,cAAc,CAAC0B,OAAO,EACtBrD,WAAW,CAAC0C,OAAO,EACnBf,cAAc,CAACJ,EAAG,CACnB,CACF;MACD+B,GAAG,EAAEtD,WAAW,CAAC0C,OAAO;MACxBa,IAAI,EAAEzD,YAAY,CAAC4C,OAAO;MAC1Bc,eAAe,EAAE,CACfzD,gBAAgB,CAAC0D,KAAK,EACtB1D,gBAAgB,CAAC2D,gBAAgB,EACjC3D,gBAAgB,CAAC4D,WAAW,EAC5B5D,gBAAgB,CAACoB,MAAM,EACvBpB,gBAAgB,CAAC6D,OAAO;KAE3B;IACD,OAAOjB,QAAQ;EACjB;EAEQS,wBAAwBA,CAC9BC,OAAoB,EACpBQ,SAAiB,EACjBC,SAAiB;IAEjB,OAAOT,OAAO,CAACU,GAAG,CAAEC,MAAM,IAAI;MAC5B,MAAM5B,QAAQ,GAAe,CAC3B,GAAG,IAAI,CAACa,kBAAkB,CACxBe,MAAM,CAACd,eAAgB,EACvBc,MAAM,CAACb,oBAAqB,EAC5B,QAAQpD,gBAAgB,CAACoB,MAAM,IAAI6C,MAAM,CAACzC,EAAE,EAAE,CAC/C,EACD,GAAG,IAAI,CAAC0C,oBAAoB,CAC1B,IAAI,CAACb,wBAAwB,CAC3BY,MAAM,CAACE,YAAY,IAAI,EAAE,EACzB,QAAQnE,gBAAgB,CAACoB,MAAM,IAAI6C,MAAM,CAACzC,EAAE,EAAE,EAC9CuC,SAAS,CACV,CACF,CACF;MAED;MACA,IAAIE,MAAM,CAAClB,QAAQ,CAACC,MAAM,GAAG,CAAC,EAAE;QAC9BX,QAAQ,CAAC+B,OAAO,CACd,IAAI,CAACnB,qBAAqB,CACxBgB,MAAM,CAAClB,QAAQ,EACf,QAAQ/C,gBAAgB,CAACoB,MAAM,IAAI6C,MAAM,CAACzC,EAAE,EAAE,EAC9CuC,SAAS,CACV,CACF;;MAGH,OAAO;QACLlB,IAAI,EAAEoB,MAAM,CAACpB,IAAI;QACjBR,QAAQ,EAAEA,QAAQ;QAClBS,QAAQ,EAAE9C,gBAAgB,CAACoB,MAAM;QACjCoC,IAAI,EAAEzD,YAAY,CAACqB,MAAM;QACzBiD,IAAI,EAAE,IAAI,CAAC7D,iBAAiB,CAAC8D,gBAAgB,CAACL,MAAM,CAAC;QACrDV,GAAG,EAAE,QAAQvD,gBAAgB,CAACoB,MAAM,IAAI6C,MAAM,CAACzC,EAAE,EAAE;QACnDsC,SAAS,EAAEA,SAAS;QACpBS,WAAW,EAAE,IAAI;QACjBd,eAAe,EAAE,CACfzD,gBAAgB,CAAC0D,KAAK,EACtB1D,gBAAgB,CAAC2D,gBAAgB,EACjC3D,gBAAgB,CAAC4D,WAAW,EAC5B5D,gBAAgB,CAACoB,MAAM,EACvBpB,gBAAgB,CAAC6D,OAAO;OAE3B;IACH,CAAC,CAAC;EACJ;EAEQX,kBAAkBA,CACxBC,eAAgC,EAChCC,oBAA2C,EAC3CU,SAAiB;IAEjB,MAAMU,SAAS,GAAe,EAAE;IAChC,IAAIrB,eAAe,CAACH,MAAM,GAAG,CAAC,EAAE;MAC9BwB,SAAS,CAAChC,IAAI,CAAC;QACbK,IAAI,EAAE,SAAS;QACfR,QAAQ,EAAE,IAAI,CAAC6B,oBAAoB,CACjCf,eAAe,CAACa,GAAG,CAAES,SAAS,KAAM;UAClC5B,IAAI,EAAE4B,SAAS,CAAC5B,IAAI;UACpBR,QAAQ,EAAE,IAAI,CAAC6B,oBAAoB,CACjC,IAAI,CAACQ,mBAAmB,CAACD,SAAS,CAAC,CACpC;UACD3B,QAAQ,EAAE2B,SAAS,CAACE,aAAa,GAC7B3E,gBAAgB,CAAC2D,gBAAgB,GACjC3D,gBAAgB,CAAC0D,KAAK;UAC1BH,GAAG,EAAE,QAAQvD,gBAAgB,CAAC0D,KAAK,IAAIe,SAAS,CAACjD,EAAE,EAAE;UACrDgC,IAAI,EAAEiB,SAAS,CAACE,aAAa,GACzB5E,YAAY,CAAC6E,WAAW,GACxB7E,YAAY,CAAC2D,KAAK;UACtBI,SAAS,EAAE,GAAG7D,WAAW,CAAC4E,YAAY,IAAIf,SAAS,EAAE;UACrDO,IAAI,EAAE,IAAI,CAAC7D,iBAAiB,CAACsE,sBAAsB,CACjDL,SAAS,EACT,IAAI,CAACjE,iBAAiB,CAACuE,mBAAmB,CACxCN,SAAS,CAACO,UAAU,IAAI,EAAE,CAC3B,CACF;UACDT,WAAW,EAAE,IAAI;UACjBd,eAAe,EAAE,CACfzD,gBAAgB,CAACiF,SAAS,EAC1BjF,gBAAgB,CAACkF,SAAS;SAE7B,CAAC,CAAC,CACJ;QACDzB,eAAe,EAAE,CACfzD,gBAAgB,CAAC0D,KAAK,EACtB1D,gBAAgB,CAAC2D,gBAAgB,CAClC;QACDb,QAAQ,EAAE5C,oBAAoB;QAC9BqD,GAAG,EAAE,GAAGtD,WAAW,CAAC4E,YAAY,IAAIf,SAAS,EAAE;QAC/CA,SAAS,EAAEA,SAAS;QACpBN,IAAI,EAAEzD,YAAY,CAAC2D;OACpB,CAAC;;IAEJ,IAAIN,oBAAoB,CAACJ,MAAM,GAAG,CAAC,EAAE;MACnCwB,SAAS,CAAChC,IAAI,CAAC;QACbK,IAAI,EAAE,cAAc;QACpBR,QAAQ,EAAE,IAAI,CAAC6B,oBAAoB,CACjC,IAAI,CAACiB,uBAAuB,CAAC/B,oBAAoB,EAAEU,SAAS,CAAC,CAC9D;QACDhB,QAAQ,EAAEzC,mBAAmB;QAC7BmD,IAAI,EAAEzD,YAAY,CAAC6D,WAAW;QAC9BL,GAAG,EAAE,GAAGtD,WAAW,CAACmF,kBAAkB,IAAItB,SAAS,EAAE;QACrDA,SAAS,EAAEA,SAAS;QACpBL,eAAe,EAAE,CAACzD,gBAAgB,CAAC4D,WAAW;OAC/C,CAAC;;IAEJ,OAAOY,SAAS;EAClB;EAEQvB,qBAAqBA,CAC3BF,QAAmB,EACnBe,SAAiB,EACjBC,SAAiB;IAEjB,OAAO;MACLlB,IAAI,EAAEzC,kBAAkB;MACxBiC,QAAQ,EAAEU,QAAQ,CAACiB,GAAG,CAAEzC,OAAO,KAAM;QACnCsB,IAAI,EAAEtB,OAAO,CAACsB,IAAI;QAClBR,QAAQ,EAAE,EAAE;QACZS,QAAQ,EAAE9C,gBAAgB,CAAC6D,OAAO;QAClCL,IAAI,EAAEzD,YAAY,CAAC8D,OAAO;QAC1BN,GAAG,EAAE,QAAQvD,gBAAgB,CAAC6D,OAAO,IAAItC,OAAO,CAACC,EAAE,EAAE;QACrD6C,IAAI,EAAE;UAAE,GAAG9C,OAAO;UAAE8D,SAAS,EAAEtB;QAAS,CAAE;QAC1CD,SAAS,EAAE,GAAG7D,WAAW,CAACqF,cAAc,IAAIxB,SAAS,EAAE;QACvDS,WAAW,EAAE;OACd,CAAC,CAAC;MACHhB,GAAG,EAAE,GAAGtD,WAAW,CAACqF,cAAc,IAAIxB,SAAS,EAAE;MACjDA,SAAS,EAAEA,SAAS;MACpBhB,QAAQ,EAAE3C,sBAAsB;MAChCqD,IAAI,EAAEzD,YAAY,CAAC8D,OAAO;MAC1BJ,eAAe,EAAE,CAACzD,gBAAgB,CAAC6D,OAAO;KAC3C;EACH;EAEQsB,uBAAuBA,CAC7B/B,oBAA2C,EAC3CU,SAAiB;IAEjB,OAAOV,oBAAoB,CAACY,GAAG,CAAEuB,QAAQ,IAAI;MAC3C;MACA,IAAI,CAAC3E,aAAa,CAAC4E,iBAAiB,CAAC;QACnChE,EAAE,EAAE+D,QAAQ,CAAC/D,EAAE,EAAEiE,QAAQ,EAAG;QAC5B5C,IAAI,EAAE0C,QAAQ,EAAE1C,IAAI;QACpB6C,aAAa,EAAE;OAChB,CAAC;MAEF;MACA,OAAO;QACL7C,IAAI,EAAE0C,QAAQ,CAAC1C,IAAI;QACnBR,QAAQ,EAAE,IAAI,CAACsD,qBAAqB,CAACJ,QAAQ,CAAC;QAC9CzC,QAAQ,EAAE9C,gBAAgB,CAAC4D,WAAW;QACtCL,GAAG,EAAE,QAAQvD,gBAAgB,CAAC4D,WAAW,IAAI2B,QAAQ,CAAC/D,EAAE,EAAE;QAC1DsC,SAAS,EAAE,GAAG7D,WAAW,CAACmF,kBAAkB,IAAItB,SAAS,EAAE;QAC3DN,IAAI,EAAEzD,YAAY,CAAC6D,WAAW;QAC9BS,IAAI,EAAE,IAAI,CAAC7D,iBAAiB,CAACoF,qBAAqB,CAChDL,QAAQ,EACR,IAAI,CAAC/E,iBAAiB,CAACqF,iBAAiB,CACtCN,QAAQ,CAACO,mBAAmB,IAAI,EAAE,CACnC,CACF;QACDvB,WAAW,EAAE,IAAI;QACjBd,eAAe,EAAE,CAACzD,gBAAgB,CAAC+F,kBAAkB;OACtD;IACH,CAAC,CAAC;EACJ;EAEQJ,qBAAqBA,CAACJ,QAA6B;IACzD,OACEA,QAAQ,CAACO,mBAAmB,EAAE9B,GAAG,CAAEgC,OAAO,KAAM;MAC9CnD,IAAI,EAAEmD,OAAO,CAACnD,IAAI;MAClBR,QAAQ,EAAE,EAAE;MACZS,QAAQ,EAAE9C,gBAAgB,CAAC+F,kBAAkB;MAC7CvC,IAAI,EAAEzD,YAAY,CAACgG,kBAAkB;MACrCxC,GAAG,EAAE,QAAQvD,gBAAgB,CAAC+F,kBAAkB,IAAIC,OAAO,CAACxE,EAAE,EAAE;MAChEsC,SAAS,EAAE,QAAQ9D,gBAAgB,CAAC4D,WAAW,IAAI2B,QAAQ,CAAC/D,EAAE,EAAE;MAChEiC,eAAe,EAAE,EAAE;MACnBY,IAAI,EAAE;QACJ,GAAG,IAAI,CAAC7D,iBAAiB,CAACqF,iBAAiB,CAAC,CAACG,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;QACzDC,qBAAqB,EAAEV,QAAQ,CAAC/D;;KAEnC,CAAC,CAAC,IAAI,EAAE;EAEb;EAEQkD,mBAAmBA,CAACD,SAAwB;IAClD,OAAOA,SAAS,CAACO,UAAU,CAAChB,GAAG,CAAEkC,IAAI,KAAM;MACzCrD,IAAI,EAAEqD,IAAI,CAACrD,IAAI;MACfR,QAAQ,EAAE,EAAE;MACZS,QAAQ,EACNoD,IAAI,CAACpD,QAAQ,IAAIhD,mBAAmB,CAACqG,SAAS,GAC1CnG,gBAAgB,CAACkF,SAAS,GAC1BlF,gBAAgB,CAACiF,SAAS;MAChCzB,IAAI,EACF0C,IAAI,CAACpD,QAAQ,IAAIhD,mBAAmB,CAACqG,SAAS,GAC1CpG,YAAY,CAACmF,SAAS,GACtBnF,YAAY,CAACkF,SAAS;MAC5B1B,GAAG,EAAE,QACH2C,IAAI,CAACpD,QAAQ,IAAIhD,mBAAmB,CAACqG,SAAS,GAC1CnG,gBAAgB,CAACkF,SAAS,GAC1BlF,gBAAgB,CAACiF,SACvB,IAAIiB,IAAI,CAAC1E,EAAE,EAAE;MACbsC,SAAS,EAAE,QAAQ9D,gBAAgB,CAAC0D,KAAK,IAAIe,SAAS,CAACjD,EAAE,EAAE;MAC3D6C,IAAI,EAAE;QACJ,GAAG,IAAI,CAAC7D,iBAAiB,CAACuE,mBAAmB,CAAC,CAACmB,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;QACxDE,eAAe,EAAE3B,SAAS,CAACjD;OAC5B;MACDiC,eAAe,EAAE;KAClB,CAAC,CAAC;EACL;EAEA;;;;;;;;EAQA4C,kBAAkBA,CAACC,QAAkB;IACnC,MAAM1D,QAAQ,GAAG,IAAI,CAAC/B,oBAAoB,EAAE;IAC5C,IAAI,CAAC+B,QAAQ,EAAE;IAEf,MAAM2D,eAAe,GAAG;MAAE,GAAG3D;IAAQ,CAAE;IACvC,MAAM4D,qBAAqB,GACzBF,QAAQ,CAACxD,QAAQ,KAAK9C,gBAAgB,CAACoB,MAAM,IAC7CkF,QAAQ,CAACxC,SAAS,KAAK7D,WAAW,CAAC0C,OAAO;IAE5C,IAAI6D,qBAAqB,EAAE;MACzB,IAAI,CAACC,iBAAiB,CAACH,QAAQ,EAAEC,eAAe,CAAClE,QAAQ,CAAC;KAC3D,MAAM;MACL,MAAMqE,UAAU,GAAG,IAAI,CAACC,aAAa,CAACL,QAAQ,CAACxC,SAAU,CAAC;MAC1D,IAAI4C,UAAU,EAAE;QACd,IAAI,CAACE,eAAe,CAACN,QAAQ,EAAEI,UAAU,CAAC;OAC3C,MAAM;QACL;QACA,IAAI,CAACG,sBAAsB,CAACP,QAAQ,EAAEC,eAAe,CAAC;;;IAI1D;IACAA,eAAe,CAAClE,QAAQ,GAAG,IAAI,CAACyE,aAAa,CAACP,eAAe,CAAClE,QAAQ,CAAC;IACvE,IAAI,CAACxB,oBAAoB,CAACkB,GAAG,CAACwE,eAAe,CAAC;IAC9C,IAAI,CAACzF,qBAAqB,CAACkB,IAAI,CAACuE,eAAe,CAAC;EAClD;EAEQE,iBAAiBA,CAACxE,IAAc,EAAEI,QAAoB;IAC5DA,QAAQ,CAACG,IAAI,CAACP,IAAI,CAAC;IACnB,IAAI,CAACiC,oBAAoB,CAAC7B,QAAQ,CAAC;EACrC;EAEQuE,eAAeA,CAAC3E,IAAc,EAAEyE,UAAoB;IAC1D,IAAIA,UAAU,CAAC5D,QAAQ,KAAK9C,gBAAgB,CAACoB,MAAM,EAAE;MACnD,IAAIa,IAAI,CAACa,QAAQ,KAAK9C,gBAAgB,CAACoB,MAAM,EAAE;QAC7C,IAAI,CAACyF,sBAAsB,CAAC5E,IAAI,EAAEyE,UAAU,CAAC;OAC9C,MAAM;QACL,IAAI,CAACD,iBAAiB,CAACxE,IAAI,EAAEyE,UAAU,CAACrE,QAAQ,CAAC;;KAEpD,MAAM;MACL,IAAI,CAACoE,iBAAiB,CAACxE,IAAI,EAAEyE,UAAU,CAACrE,QAAQ,CAAC;;EAErD;EAEA;;;;;;;;EAQQyE,aAAaA,CAACC,KAAiB;IACrC,OAAOA,KAAK,CAACC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAI;MACzB;MACA,MAAMC,MAAM,GAAG,IAAI,CAAChG,iBAAiB,CAACiG,OAAO,CAACH,CAAC,CAACnE,QAAQ,CAAC;MACzD,MAAMuE,MAAM,GAAG,IAAI,CAAClG,iBAAiB,CAACiG,OAAO,CAACF,CAAC,CAACpE,QAAQ,CAAC;MAEzD;MACA,IAAIqE,MAAM,KAAK,CAAC,CAAC,EAAE,OAAO,CAAC;MAC3B,IAAIE,MAAM,KAAK,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC;MAE5B;MACA,OAAOF,MAAM,GAAGE,MAAM;IACxB,CAAC,CAAC;EACJ;EAEQnD,oBAAoBA,CAAC6C,KAAiB;IAC5C,OAAOA,KAAK,CAACC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KACrBD,CAAC,CAACpE,IAAI,CAACyE,aAAa,CAACJ,CAAC,CAACrE,IAAI,EAAE0E,SAAS,EAAE;MAAEC,WAAW,EAAE;IAAM,CAAE,CAAC,CACjE;EACH;EAEA;;;;;;;;;EASQX,sBAAsBA,CAC5BY,YAAsB,EACtBf,UAAoB;IAEpB;IACA,IAAIgB,WAAW,GAAGhB,UAAU,CAACrE,QAAQ,CAACsF,IAAI,CACvC1F,IAAI,IACHA,IAAI,CAACsB,GAAG,IACR,GACEkE,YAAY,CAAC3E,QAAQ,IAAI9C,gBAAgB,CAAC0D,KAAK,IAC/C+D,YAAY,CAAC3E,QAAQ,IAAI9C,gBAAgB,CAAC2D,gBAAgB,GACtD1D,WAAW,CAAC4E,YAAY,GACxB4C,YAAY,CAAC3E,QAAQ,KAAK9C,gBAAgB,CAAC6D,OAAO,GAClD5D,WAAW,CAACqF,cAAc,GAC1BrF,WAAW,CAACmF,kBAClB,IAAIqC,YAAY,CAAC3D,SAAS,EAAE,CAC/B;IAED,IAAI4D,WAAW,EAAE;MACf;MACAA,WAAW,CAACrF,QAAQ,CAACG,IAAI,CAACiF,YAAY,CAAC;MACvC,IAAI,CAACvD,oBAAoB,CAACwD,WAAW,CAACrF,QAAQ,CAAC;KAChD,MAAM;MACL;MACAqF,WAAW,GAAG,IAAI,CAACE,oBAAoB,CAACH,YAAY,EAAEf,UAAU,CAACnD,GAAG,CAAC;MACrEmD,UAAU,EAAErE,QAAQ,CAACG,IAAI,CAAC;QACxB,GAAGkF,WAAW;QACdrF,QAAQ,EAAE,IAAI,CAAC6B,oBAAoB,CAAC,CAClC,GAAGwD,WAAW,CAACrF,QAAQ,EACvBoF,YAAY,CACb;OACF,CAAC;MACF,IAAI,CAACX,aAAa,CAACJ,UAAU,CAACrE,QAAQ,CAAC;;EAE3C;EAEAwF,mBAAmBA,CAACC,QAAkB;IACpC,MAAMlF,QAAQ,GAAG,IAAI,CAAC/B,oBAAoB,EAAE;IAC5C,IAAI,CAAC+B,QAAQ,EAAE;IAEf,MAAM8D,UAAU,GAAG,IAAI,CAACC,aAAa,CAACmB,QAAQ,CAAChE,SAAU,CAAC;IAC1D,IAAI4C,UAAU,EAAE;MAEZA,UAAU,CAACrC,IACZ,CAAC0D,KAAK,CAACvF,IAAI,CACVsF,QAAQ,CAACzD,IAAyD,CACnE;MACDqC,UAAU,CAACrE,QAAQ,CAACG,IAAI,CAACsF,QAAQ,CAAC;MAClC,IAAI,CAAC5D,oBAAoB,CAACwC,UAAU,CAACrE,QAAQ,CAAC;MAE9C;MACA,MAAM2F,WAAW,GAAG,IAAI,CAACC,aAAa,CAACrF,QAAQ,CAAC;MAChD,IAAI,CAAC/B,oBAAoB,CAACkB,GAAG,CAACiG,WAAW,CAAC;MAC1C,IAAI,CAAClH,qBAAqB,CAACkB,IAAI,CAACgG,WAAW,CAAC;;EAEhD;EAEAE,iBAAiBA,CAACtF,QAAkB;IAClC,MAAMuF,qBAAqB,GAAG,IAAI,CAACtH,oBAAoB,EAAE;IACzD,IAAI,CAACsH,qBAAqB,EAAE;IAE5B,MAAMC,UAAU,GAAG,IAAI,CAACH,aAAa,CAACE,qBAAqB,CAAC;IAC5D,MAAMzB,UAAU,GAAG,IAAI,CAAC2B,cAAc,CAACzF,QAAQ,CAACW,GAAG,EAAE6E,UAAU,CAAC;IAEhE,IAAI1B,UAAU,EAAE;MACd,MAAM4B,YAAY,GAAG5B,UAAU,EAAErE,QAAQ,CAACsF,IAAI,CAC3C1F,IAAI,IAAKA,IAAI,CAACsB,GAAG,KAAKX,QAAQ,CAACW,GAAG,CACpC;MACD,IAAI+E,YAAY,IAAIA,YAAY,CAACjE,IAAI,IAAIzB,QAAQ,CAACyB,IAAI,EAAE;QACtDiE,YAAY,CAACzF,IAAI,GAAGD,QAAQ,CAACyB,IAAI,CAACxB,IAAI;QACtC,IACEyF,YAAY,CAACxF,QAAQ,KAAK9C,gBAAgB,CAACkF,SAAS,IACpDoD,YAAY,CAACxF,QAAQ,KAAK9C,gBAAgB,CAACiF,SAAS,IACpDqD,YAAY,CAACxF,QAAQ,KAAK9C,gBAAgB,CAAC+F,kBAAkB,EAC7D;UACA,IAAI,CAACwC,uBAAuB,CAAC7B,UAAU,EAAE9D,QAAQ,EAAEwF,UAAU,CAAC;UAC9D,IACEE,YAAY,CAACxF,QAAQ,IAAI9C,gBAAgB,CAACkF,SAAS,IACnDoD,YAAY,CAACxF,QAAQ,KAAK9C,gBAAgB,CAACiF,SAAS,EACpD;YACA,MAAM6C,QAAQ,GAAGQ,YAAY,CAACjE,IAAgC;YAC9D,IAAI,CAACmE,cAAc,CAACF,YAAY,CAACjE,IAAI,EAAE;cACrCxB,IAAI,EAAEiF,QAAQ,CAACjF,IAAI;cACnBrB,EAAE,EAAEsG,QAAQ,CAACtG,EAAE;cACfiH,WAAW,EAAEX,QAAQ,CAACW,WAAW;cACjCC,QAAQ,EAAEZ,QAAQ,CAACY;aACpB,CAAC;WACH,MAAM;YACL,MAAMZ,QAAQ,GAAGQ,YAAY,CAACjE,IAA8B;YAC5D,IAAI,CAACmE,cAAc,CAACF,YAAY,CAACjE,IAAI,EAAE;cACrCxB,IAAI,EAAEiF,QAAQ,CAACjF,IAAI;cACnBrB,EAAE,EAAEsG,QAAQ,CAACtG;aACd,CAAC;;SAEL,MAAM;UACL,MAAMmH,eAAe,GAAG/F,QAAQ,CAACyB,IAEH;UAC9B,IAAI,CAACmE,cAAc,CAACF,YAAY,CAACjE,IAAI,EAAE;YACrCxB,IAAI,EAAE8F,eAAe,CAAC9F,IAAI;YAC1BrB,EAAE,EAAEmH,eAAe,CAACnH,EAAE;YACtBoH,KAAK,EAAED,eAAe,CAACC,KAAK;YAC5BH,WAAW,EAAEE,eAAe,CAACF,WAAW;YACxClF,GAAG,EAAEoF,eAAe,CAACpF,GAAG;YACxBsF,SAAS,EAAEF,eAAe,CAACE,SAAS;YACpCC,WAAW,EAAEH,eAAe,CAACG,WAAW;YACxCC,QAAQ,EAAEJ,eAAe,CAACI,QAAQ;YAClCC,IAAI,EAAEL,eAAe,CAACK;WACvB,CAAC;;QAEJ,IAAI,CAAC9E,oBAAoB,CAACwC,UAAU,EAAErE,QAAQ,CAAC;QAC/C,IAAI,CAACyE,aAAa,CAACJ,UAAU,EAAErE,QAAQ,CAAC;QACxC,IAAI,CAACxB,oBAAoB,CAACkB,GAAG,CAACqG,UAAU,CAAC;QACzC,IAAI,CAACtH,qBAAqB,CAACkB,IAAI,CAACoG,UAAU,CAAC;;;EAGjD;EAEQI,cAAcA,CACpBF,YAAe,EACfW,YAAwB;IAExBC,MAAM,CAACC,IAAI,CAACF,YAAY,CAAC,CAAC3G,OAAO,CAAE8G,GAAG,IAAI;MACxC,IAAIA,GAAG,IAAId,YAAY,EAAE;QACtBA,YAAoB,CAACc,GAAG,CAAC,GAAIH,YAAoB,CAACG,GAAG,CAAC;;IAE3D,CAAC,CAAC;EACJ;EAEAb,uBAAuBA,CACrBc,SAAmB,EACnBzG,QAAkB,EAClB5B,cAAwB;IAExB,IAAIqI,SAAS,CAAChF,IAAI,IAAIzB,QAAQ,CAACyB,IAAI,EAAE;MAEjCgF,SAAS,CAAChF,IACX,CAAC0D,KAAK,CAACzF,OAAO,CAAEgH,IAAI,IAAI;QACvB,IAAIA,IAAI,CAAC9H,EAAE,IAAIoB,QAAQ,CAACyB,IAAI,EAAE7C,EAAE,EAAE;UAChC0H,MAAM,CAACK,MAAM,CAACD,IAAI,EAAE1G,QAAQ,CAACyB,IAAI,CAAC;;MAEtC,CAAC,CAAC;MACF,IAAI,CAACxD,oBAAoB,CAACkB,GAAG,CAACf,cAAc,CAAC;MAC7C,IAAI,CAACF,qBAAqB,CAACkB,IAAI,CAAChB,cAAc,CAAC;;EAEnD;EAEAwI,mBAAmBA,CAAC5G,QAAkB;IACpC,MAAMuF,qBAAqB,GAAG,IAAI,CAACtH,oBAAoB,EAAE;IACzD,IAAI,CAACsH,qBAAqB,EAAE;IAE5B,MAAMC,UAAU,GAAG,IAAI,CAACH,aAAa,CAACE,qBAAqB,CAAC;IAC5D,MAAMzB,UAAU,GAAG,IAAI,CAAC2B,cAAc,CAACzF,QAAQ,CAACW,GAAG,EAAE6E,UAAU,CAAC;IAEhE,IAAI1B,UAAU,EAAE;MACd,MAAM+C,KAAK,GAAG/C,UAAU,EAAErE,QAAQ,CAACqH,SAAS,CACzCzH,IAAI,IAAKA,IAAI,CAACsB,GAAG,KAAKX,QAAQ,CAACW,GAAG,CACpC;MACD,IACEX,QAAQ,CAACE,QAAQ,KAAK9C,gBAAgB,CAACkF,SAAS,IAChDtC,QAAQ,CAACE,QAAQ,KAAK9C,gBAAgB,CAACiF,SAAS,IAChDrC,QAAQ,CAACE,QAAQ,KAAK9C,gBAAgB,CAAC+F,kBAAkB,EACzD;QAEEW,UAAU,CAACrC,IACZ,CAAC0D,KAAK,GACLrB,UAAU,CAACrC,IACZ,CAAC0D,KAAK,CAAC4B,MAAM,CAAEL,IAAI,IAAKA,IAAI,CAAC9H,EAAE,KAAKoB,QAAQ,CAACyB,IAAI,EAAE7C,EAAE,CAAC;;MAEzD,IAAIiI,KAAK,GAAG,CAAC,CAAC,EAAE;QACd/C,UAAU,EAAErE,QAAQ,CAACuH,MAAM,CAACH,KAAK,EAAE,CAAC,CAAC;QACrC,IACE/C,UAAU,CAACrE,QAAQ,CAACW,MAAM,IAAI,CAAC,KAC9B0D,UAAU,CAAC5D,QAAQ,IAAI5C,oBAAoB,IAC1CwG,UAAU,CAAC5D,QAAQ,IAAIzC,mBAAmB,IAC1CqG,UAAU,CAAC5D,QAAQ,IAAI3C,sBAAsB,CAAC,EAChD;UACA,MAAM0J,sBAAsB,GAAG,IAAI,CAACxB,cAAc,CAChD3B,UAAU,CAACnD,GAAG,EACd6E,UAAU,CACX;UACD,IAAIyB,sBAAsB,EAAE;YAC1BA,sBAAsB,CAACxH,QAAQ,GAC7BwH,sBAAsB,CAACxH,QAAQ,CAACsH,MAAM,CACnCpH,KAAe,IAAKA,KAAK,CAACgB,GAAG,KAAKmD,UAAU,CAACnD,GAAG,CAClD;;;QAGP,IAAI,CAAC1C,oBAAoB,CAACkB,GAAG,CAACqG,UAAU,CAAC;QACzC,IAAI,CAACtH,qBAAqB,CAACkB,IAAI,CAACoG,UAAU,CAAC;;;EAGjD;EAEA0B,QAAQA,CAACC,YAAsB,EAAEC,WAAqB;IACpD;IACA,MAAM7B,qBAAqB,GAAG,IAAI,CAACtH,oBAAoB,EAAE;IACzD,IAAI,CAACsH,qBAAqB,EAAE;IAE5B,MAAMC,UAAU,GAAG,IAAI,CAACH,aAAa,CAACE,qBAAqB,CAAC;IAE5D;IACA,MAAMzB,UAAU,GAAG,IAAI,CAAC2B,cAAc,CAAC2B,WAAW,CAACzG,GAAG,EAAE6E,UAAU,CAAC;IAEnE,IACE,CAAC,IAAI,CAAC6B,mBAAmB,CACvBF,YAAY,EACZC,WAAW,EACXtD,UAAU,EACV0B,UAAU,CACX,EAED;IAEF,IAAI1B,UAAU,EAAE;MACdA,UAAU,CAACrE,QAAQ,GAAGqE,UAAU,CAACrE,QAAQ,CAACsH,MAAM,CAC7CpH,KAAe,IAAKA,KAAK,CAACgB,GAAG,KAAKyG,WAAW,CAACzG,GAAG,CACnD;MACD,IACEmD,UAAU,CAACrE,QAAQ,CAACW,MAAM,IAAI,CAAC,KAC9B0D,UAAU,CAAC5D,QAAQ,IAAI5C,oBAAoB,IAC1CwG,UAAU,CAAC5D,QAAQ,IAAIzC,mBAAmB,IAC1CqG,UAAU,CAAC5D,QAAQ,IAAI3C,sBAAsB,CAAC,EAChD;QACA,MAAM0J,sBAAsB,GAAG,IAAI,CAACxB,cAAc,CAChD3B,UAAU,CAACnD,GAAG,EACd6E,UAAU,CACX;QACD,IAAIyB,sBAAsB,EAAE;UAC1BA,sBAAsB,CAACxH,QAAQ,GAC7BwH,sBAAsB,CAACxH,QAAQ,CAACsH,MAAM,CACnCpH,KAAe,IAAKA,KAAK,CAACgB,GAAG,KAAKmD,UAAU,CAACnD,GAAG,CAClD;;;;IAKT;IACA,IACEyG,WAAW,CAAClH,QAAQ,KAAK9C,gBAAgB,CAACoB,MAAM,IAChD2I,YAAY,CAACjH,QAAQ,IAAI5C,oBAAoB,IAC7C6J,YAAY,CAACjH,QAAQ,IAAIzC,mBAAmB,IAC5C0J,YAAY,CAACjH,QAAQ,IAAI3C,sBAAsB,EAC/C;MACA4J,YAAY,CAAC1H,QAAQ,CAACG,IAAI,CAAC;QACzB,GAAGwH,WAAW;QACdlG,SAAS,EAAEiG,YAAY,CAACxG;OACzB,CAAC;MACF,IAAI,CAACW,oBAAoB,CAAC6F,YAAY,CAAC1H,QAAQ,CAAC;MAChD,IAAI,CAACyE,aAAa,CAACiD,YAAY,CAAC1H,QAAQ,CAAC;MACzC,IAAI,CAAC6H,gBAAgB,CAACH,YAAY,EAAEC,WAAW,CAAC;KACjD,MAAM;MACL,MAAMG,gBAAgB,GAAG,IAAI,CAACC,mBAAmB,CAC/CL,YAAY,CAACxG,GAAG,EAChB6E,UAAU,CACX;MACD,IAAI+B,gBAAgB,EAAE;QACpB,MAAMzC,WAAW,GAAG,IAAI,CAACE,oBAAoB,CAC3CoC,WAAW,EACXG,gBAAgB,CAAC5G,GAAG,CACrB;QACD,MAAM8G,mBAAmB,GAAGF,gBAAgB,EAAE9H,QAAQ,CAACsF,IAAI,CACxD1F,IAAc,IAAKA,IAAI,CAACsB,GAAG,KAAKmE,WAAW,CAACnE,GAAG,CACjD;QACD,IAAI,CAAC2G,gBAAgB,CAACC,gBAAgB,EAAEH,WAAW,CAAC;QACpD,IAAIK,mBAAmB,EAAE;UACvBA,mBAAmB,CAAChI,QAAQ,CAACG,IAAI,CAAC;YAChC,GAAGwH,WAAW;YACdlG,SAAS,EAAEuG,mBAAmB,CAAC9G;WAChC,CAAC;UAEF,IAAI,CAACW,oBAAoB,CAACmG,mBAAmB,CAAChI,QAAQ,CAAC;SACxD,MAAM;UACL8H,gBAAgB,CAAC9H,QAAQ,CAACG,IAAI,CAAC;YAC7B,GAAGkF,WAAW;YACdrF,QAAQ,EAAE,CACR,GAAGqF,WAAW,CAACrF,QAAQ,EACvB;cAAE,GAAG2H,WAAW;cAAElG,SAAS,EAAE4D,WAAW,CAACnE;YAAG,CAAE,CAC/C;YACDO,SAAS,EAAEiG,YAAY,CAACxG;WACzB,CAAC;UACF,IAAI,CAACuD,aAAa,CAACqD,gBAAgB,CAAC9H,QAAQ,CAAC;;;;IAKnD;IACA,IAAI,CAACxB,oBAAoB,CAACkB,GAAG,CAACqG,UAAU,CAAC;IACzC,IAAI,CAACtH,qBAAqB,CAACkB,IAAI,CAACoG,UAAU,CAAC;EAC7C;EAEQ6B,mBAAmBA,CACzBK,UAAoB,EACpBN,WAAqB,EACrBtD,UAA2B,EAC3B1F,cAAwB;IAExB,IACE0F,UAAU,EAAE5C,SAAS,KAAK9C,cAAc,EAAEuC,GAAG,IAC7C+G,UAAU,CAACxH,QAAQ,KAAK9C,gBAAgB,CAAC2C,OAAO,IAChDqH,WAAW,CAAClH,QAAQ,KAAK9C,gBAAgB,CAACoB,MAAM,EAChD;MACA,OAAO,KAAK;;IAEd,IACE4I,WAAW,CAACzG,GAAG,KAAK+G,UAAU,CAAC/G,GAAG,IAClCyG,WAAW,CAACzG,GAAG,IAAI+G,UAAU,CAACxG,SAAS,IACvCkG,WAAW,CAAClG,SAAS,IAAIwG,UAAU,CAAC/G,GAAG,EAEvC,OAAO,KAAK;IAEd,IAAI+G,UAAU,CAACxH,QAAQ,KAAK9C,gBAAgB,CAAC6D,OAAO,EAAE,OAAO,KAAK;IAElE;IACA,IAAI0G,aAAa,GAAG,IAAI,CAAClC,cAAc,CAACiC,UAAU,CAAC/G,GAAG,EAAEvC,cAAc,CAAC;IACvE,IACE,CAACsJ,UAAU,CAACxH,QAAQ,IAAI5C,oBAAoB,IAC1CoK,UAAU,CAACxH,QAAQ,IAAI3C,sBAAsB,IAC7CmK,UAAU,CAACxH,QAAQ,IAAIzC,mBAAmB,KAC5CkK,aAAa,EAAEzH,QAAQ,IAAI9C,gBAAgB,CAACoB,MAAM,EAClD;MACA,OAAO,KAAK;;IAEd,OAAOmJ,aAAa,EAAE;MACpB,IAAIA,aAAa,CAAChH,GAAG,KAAKyG,WAAW,CAACzG,GAAG,EAAE;QACzC,OAAO,KAAK,CAAC,CAAC;;MAEhBgH,aAAa,GAAG,IAAI,CAAClC,cAAc,CAACkC,aAAa,CAAChH,GAAG,EAAEvC,cAAc,CAAC;;IAExE,OAAO,IAAI;EACb;EAEAqH,cAAcA,CAACmC,OAAe,EAAEvG,MAAgB;IAC9C,IAAIA,MAAM,CAAC5B,QAAQ,EAAE;MACnB,KAAK,IAAIE,KAAK,IAAI0B,MAAM,CAAC5B,QAAQ,EAAE;QACjC,IAAIE,KAAK,CAACgB,GAAG,KAAKiH,OAAO,EAAE;UACzB,OAAOvG,MAAM;;QAEf,MAAMhC,IAAI,GAAG,IAAI,CAACoG,cAAc,CAACmC,OAAO,EAAEjI,KAAK,CAAC;QAChD,IAAIN,IAAI,EAAE,OAAOA,IAAI;;;IAGzB,OAAO,IAAI;EACb;EAEA0E,aAAaA,CAACpD,GAAW;IACvB,IAAIA,GAAG,IAAItD,WAAW,CAAC0C,OAAO,EAAE;MAC9B,OAAO,IAAI,CAAC9B,oBAAoB,EAAE;;IAEpC,MAAMqB,WAAW,GAAG,IAAI,CAACnB,mBAAmB,EAAE;IAC9C,OAAOmB,WAAW,CAACyF,IAAI,CAAE1F,IAAI,IAAKA,IAAI,CAACsB,GAAG,IAAIA,GAAG,CAAC,IAAI,IAAI;EAC5D;EAEQ6G,mBAAmBA,CAAC7G,GAAW,EAAEkH,IAAc;IACrD,IAAIA,IAAI,CAAClH,GAAG,KAAKA,GAAG,EAAE;MACpB,OAAOkH,IAAI;;IAEb,IAAIA,IAAI,CAACpI,QAAQ,EAAE;MACjB,KAAK,MAAME,KAAK,IAAIkI,IAAI,CAACpI,QAAQ,EAAE;QACjC,MAAMqI,KAAK,GAAG,IAAI,CAACN,mBAAmB,CAAC7G,GAAG,EAAEhB,KAAK,CAAC;QAClD,IAAImI,KAAK,EAAE,OAAOA,KAAK;;;IAG3B,OAAO,IAAI;EACb;EAEQ9C,oBAAoBA,CAC1BoC,WAAqB,EACrBlG,SAAiB;IAEjB,MAAM6G,eAAe,GACnBX,WAAW,CAAClH,QAAQ,KAAK9C,gBAAgB,CAAC0D,KAAK,IAC/CsG,WAAW,CAAClH,QAAQ,KAAK9C,gBAAgB,CAAC2D,gBAAgB,GACtD,SAAS,GACTqG,WAAW,CAAClH,QAAQ,KAAK9C,gBAAgB,CAAC6D,OAAO,GACjD,UAAU,GACV,cAAc;IACpB,MAAM+G,cAAc,GAClBZ,WAAW,CAAClH,QAAQ,KAAK9C,gBAAgB,CAAC0D,KAAK,IAC/CsG,WAAW,CAAClH,QAAQ,KAAK9C,gBAAgB,CAAC2D,gBAAgB,GACtD1D,WAAW,CAAC4E,YAAY,GACxBmF,WAAW,CAAClH,QAAQ,KAAK9C,gBAAgB,CAAC6D,OAAO,GACjD5D,WAAW,CAACqF,cAAc,GAC1BrF,WAAW,CAACmF,kBAAkB;IACpC,MAAMyF,mBAAmB,GACvBb,WAAW,CAAClH,QAAQ,KAAK9C,gBAAgB,CAAC0D,KAAK,IAC/CsG,WAAW,CAAClH,QAAQ,KAAK9C,gBAAgB,CAAC2D,gBAAgB,GACtDzD,oBAAoB,GACpB8J,WAAW,CAAClH,QAAQ,KAAK9C,gBAAgB,CAAC6D,OAAO,GACjD1D,sBAAsB,GACtBE,mBAAmB;IACzB,MAAMyK,eAAe,GACnBd,WAAW,CAAClH,QAAQ,KAAK9C,gBAAgB,CAAC0D,KAAK,IAC/CsG,WAAW,CAAClH,QAAQ,KAAK9C,gBAAgB,CAAC2D,gBAAgB,GACtD5D,YAAY,CAAC2D,KAAK,GAClBsG,WAAW,CAAClH,QAAQ,KAAK9C,gBAAgB,CAAC6D,OAAO,GACjD9D,YAAY,CAAC8D,OAAO,GACpB9D,YAAY,CAAC6D,WAAW;IAC9B,OAAO;MACLf,IAAI,EAAE8H,eAAe;MACrBtI,QAAQ,EAAE,EAAE;MACZS,QAAQ,EAAE+H,mBAAmB;MAC7BtH,GAAG,EAAE,GAAGqH,cAAc,IAAI9G,SAAS,EAAE;MACrCA,SAAS,EAAEA,SAAS;MACpBN,IAAI,EAAEsH,eAAe;MACrBrH,eAAe,EAAE,CAACuG,WAAW,CAAClH,QAAQ;KACvC;EACH;EAEQoH,gBAAgBA,CAACI,UAAoB,EAAEN,WAAqB;IAClE,IAAIA,WAAW,CAAC3F,IAAI,EAAE;MACpB,IAAIiG,UAAU,CAACxH,QAAQ,KAAK9C,gBAAgB,CAACoB,MAAM,EAAE;QACnD,IAAI4I,WAAW,CAAClH,QAAQ,KAAK9C,gBAAgB,CAAC0D,KAAK,EAAE;UACnD,IAAI,CAACjD,aAAa,CACfsK,qBAAqB,CAAC;YACrBvJ,EAAE,EAAE,CAACwI,WAAW,EAAE3F,IAA6B,EAAC+B,eAAe;YAC/D4E,QAAQ,EAAE,CAACV,UAAU,EAAEjG,IAAuB,EAAC2G;WAChD,CAAC,CACD1J,SAAS,EAAE;SACf,MAAM,IAAI0I,WAAW,CAAClH,QAAQ,KAAK9C,gBAAgB,CAAC4D,WAAW,EAAE;UAChE,IAAI,CAAClD,mBAAmB,CACrBuK,oBAAoB,CAAC;YACpBzJ,EAAE,EAAE,CAACwI,WAAW,EAAE3F,IAAmC,EAClD4B,qBAAqB;YACxB+E,QAAQ,EAAE,CAACV,UAAU,EAAEjG,IAAuB,EAAC2G;WAChD,CAAC,CACD1J,SAAS,EAAE;SACf,MAAM,IAAI0I,WAAW,CAAClH,QAAQ,KAAK9C,gBAAgB,CAAC6D,OAAO,EAAE;UAC5D,IAAI,CAAClD,cAAc,CAACuK,mBAAmB,CAAC;YACtC1J,EAAE,EAAE,CAACwI,WAAW,EAAE3F,IAAgB,EAAC7C,EAAG;YACtCwJ,QAAQ,EAAE,CAACV,UAAU,EAAEjG,IAAuB,EAAC2G;WAChD,CAAC;SACH,MAAM,IAAIhB,WAAW,CAAClH,QAAQ,KAAK9C,gBAAgB,CAACoB,MAAM,EAAE;UAC3D,IAAI,CAACT,cAAc,CAACwK,kBAAkB,CAAC;YACrC3J,EAAE,EAAE,CAACwI,WAAW,EAAE3F,IAAuB,EAAC2G,QAAS;YACnDI,cAAc,EAAE,CAACd,UAAU,EAAEjG,IAAuB,EAAC2G;WACtD,CAAC;;OAEL,MAAM;QACL,IAAIhB,WAAW,CAAClH,QAAQ,KAAK9C,gBAAgB,CAAC0D,KAAK,EAAE;UACnD,IAAI,CAACjD,aAAa,CACf4K,yBAAyB,CACxB,CAACrB,WAAW,EAAE3F,IAA6B,EAAC+B,eAAe,CAC5D,CACA9E,SAAS,EAAE;SACf,MAAM,IAAI0I,WAAW,CAAClH,QAAQ,KAAK9C,gBAAgB,CAAC4D,WAAW,EAAE;UAChE,IAAI,CAAClD,mBAAmB,CACrB4K,wBAAwB,CACvB,CAACtB,WAAW,EAAE3F,IAAmC,EAC9C4B,qBAAqB,CACzB,CACA3E,SAAS,EAAE;SACf,MAAM,IAAI0I,WAAW,CAAClH,QAAQ,KAAK9C,gBAAgB,CAAC6D,OAAO,EAAE;UAC5D,IAAI,CAAClD,cAAc,CAAC4K,uBAAuB,CACzC,CAACvB,WAAW,EAAE3F,IAAgB,EAAC7C,EAAG,CACnC;SACF,MAAM,IAAIwI,WAAW,CAAClH,QAAQ,KAAK9C,gBAAgB,CAACoB,MAAM,EAAE;UAC3D,IAAI,CAACT,cAAc,CAAC6K,sBAAsB,CACxC,CAACxB,WAAW,EAAE3F,IAAuB,EAAC2G,QAAS,CAChD;;;;EAIT;EAEAS,yBAAyBA,CAACC,UAAoB;IAC5C,MAAM3E,KAAK,GAAe,EAAE;IAC5B;IACA,MAAM4E,iBAAiB,GAAI1J,IAAc,IAAI;MAC3CA,IAAI,CAACI,QAAQ,CAACC,OAAO,CAAEC,KAAK,IAAI;QAC9B,IACEA,KAAK,CAACO,QAAQ,KAAK5C,oBAAoB,IACvCqC,KAAK,CAACO,QAAQ,KAAKzC,mBAAmB,EACtC;UACA0G,KAAK,CAACvE,IAAI,CAAC,GAAGD,KAAK,CAACF,QAAQ,CAAC;SAC9B,MAAM,IAAIE,KAAK,CAACO,QAAQ,KAAK9C,gBAAgB,CAACoB,MAAM,EAAE;UACrDuK,iBAAiB,CAACpJ,KAAK,CAAC;;MAE5B,CAAC,CAAC;IACJ,CAAC;IACD;IACAoJ,iBAAiB,CAACD,UAAU,CAAC;IAC7B,OAAO3E,KAAK;EACd;EAEA;;;;;;EAMA6E,aAAaA,CAACrI,GAAW;IACvB,MAAMtB,IAAI,GAAG,IAAI,CAAC0E,aAAa,CAACpD,GAAG,CAAC;IACpC,IAAItB,IAAI,EAAE,IAAI,CAACuH,mBAAmB,CAACvH,IAAI,CAAC;EAC1C;EAEA4J,cAAcA,CAAC/H,SAAiB,EAAEiD,KAAiB;IACjD,MAAM/F,cAAc,GAAG,IAAI,CAACH,oBAAoB,EAAE;IAClD,IAAIG,cAAc,EAAE;MAClB,MAAM0F,UAAU,GAAG,IAAI,CAAC2B,cAAc,CAACvE,SAAS,EAAE9C,cAAc,CAAC;MACjE,IAAI0F,UAAU,EAAE;QACd,IAAIA,UAAU,CAAC5D,QAAQ,IAAI9C,gBAAgB,CAACoB,MAAM,EAAE;UAClD,IAAI2F,KAAK,CAAC+E,IAAI,CAAE7J,IAAI,IAAKA,IAAI,CAACsB,GAAG,KAAKmD,UAAU,CAACnD,GAAG,CAAC,EAAE,OAAO,IAAI,CAAC,KAC9D,OAAO,IAAI,CAACsI,cAAc,CAACnF,UAAU,CAACnD,GAAG,EAAEwD,KAAK,CAAC;;QAExD,OAAOA,KAAK,CAAC+E,IAAI,CAAE7J,IAAI,IAAKA,IAAI,CAACsB,GAAG,KAAKmD,UAAU,CAACnD,GAAG,CAAC;OACzD,MAAM,OAAO,KAAK;KACpB,MAAM,OAAO,KAAK;EACrB;EAEAwI,4BAA4BA,CAAA;IAC1B,MAAMC,iBAAiB,GAAG,QAAQhM,gBAAgB,CAAC6D,OAAO,IAAI,IAAI,CAAC3C,gBAAgB,EAAE;IACrF,MAAMF,cAAc,GAAG,IAAI,CAACH,oBAAoB,EAAE;IAClD,IAAIG,cAAc,EAAE;MAClB,MAAM0G,WAAW,GAAG,IAAI,CAACW,cAAc,CACrC2D,iBAAiB,EACjBhL,cAAc,CACf;MACD,IAAI0G,WAAW,EAAE;QACf,OAAO,IAAI,CAACf,aAAa,CAACe,WAAW,CAAC5D,SAAU,CAAC;OAClD,MAAM,OAAO,IAAI;KACnB,MAAM,OAAO,IAAI;EACpB;EAEA;;;;;EAKQmE,aAAaA,CAAChG,IAAc;IAClC,MAAMgK,MAAM,GAAa;MACvB,GAAGhK,IAAI;MACPI,QAAQ,EAAEJ,IAAI,CAACI,QAAQ,GACnBJ,IAAI,CAACI,QAAQ,CAAC2B,GAAG,CAAEzB,KAAK,IAAK,IAAI,CAAC0F,aAAa,CAAC1F,KAAK,CAAC,CAAC,GACvD,EAAE;MACN8B,IAAI,EAAEpC,IAAI,CAACoC,IAAI,GAAG;QAAE,GAAGpC,IAAI,CAACoC;MAAI,CAAE,GAAGkD;KACtC;IACD,OAAO0E,MAAM;EACf;EAAC,QAAAC,CAAA,G;qBAh7BU5L,eAAe,EAAA6L,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,iBAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,YAAA,GAAAL,EAAA,CAAAC,QAAA,CAAAK,EAAA,CAAAC,kBAAA,GAAAP,EAAA,CAAAC,QAAA,CAAAO,EAAA,CAAAC,aAAA,GAAAT,EAAA,CAAAC,QAAA,CAAAS,EAAA,CAAAC,YAAA;EAAA;EAAA,QAAAC,EAAA,G;WAAfzM,eAAe;IAAA0M,OAAA,EAAf1M,eAAe,CAAA2M,IAAA;IAAAC,UAAA,EAFd;EAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}