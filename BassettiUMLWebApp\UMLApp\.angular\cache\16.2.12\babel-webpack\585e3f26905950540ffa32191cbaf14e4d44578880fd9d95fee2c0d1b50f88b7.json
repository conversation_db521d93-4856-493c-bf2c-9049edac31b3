{"ast": null, "code": "import _asyncToGenerator from \"D:/GitHub/Bassetti/devint-BASSETTI-GROUP-APP/BassettiUMLWebApp/UMLApp/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { EventEmitter } from '@angular/core';\nimport * as go from 'gojs';\nimport { BehaviorSubject } from 'rxjs';\nimport { GojsNodeCategory } from 'src/app/shared/model/gojs';\nimport { AccessType } from 'src/app/shared/model/project';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../api/diagram-api.service\";\nimport * as i2 from \"../access/access.service\";\nimport * as i3 from \"../project/project.service\";\nimport * as i4 from \"../property/property.service\";\nimport * as i5 from \"src/app/shared/utils/diagram-utils\";\nimport * as i6 from \"../gojs/gojs.service\";\nimport * as i7 from \"../gojs/gojsClass/gojs-class.service\";\nimport * as i8 from \"../gojs/gojsEnumeration/gojs-enumeration.service\";\nimport * as i9 from \"../gojs/gojsFolder/gojs-folder.service\";\nimport * as i10 from \"../gojs/gojs-comment/gojs-comment.service\";\nimport * as i11 from \"../gojs/gojsCommon/gojs-common.service\";\nimport * as i12 from \"../gojs/gojsAttribute/gojs-attribute.service\";\nimport * as i13 from \"../gojs/gojsLiteral/gojs-literal.service\";\nimport * as i14 from \"../gojs/gojsCardinality/gojs-cardinality.service\";\nimport * as i15 from \"../snackbar/snack-bar.service\";\nimport * as i16 from \"../pdf-export/pdf-export.service\";\nimport * as i17 from \"../treeNode/tree-node.service\";\nimport * as i18 from \"../versionHistory/version-history.service\";\nexport class DiagramService {\n  constructor(diagramApiService, accessService, projectService, propertyService, diagramUtils, gojsService, gojsClassService, goJsEnumService, goJsFolderService, gojsCommentService, gojsCommonService, gojsAttributeService, gojsLiteralService, gojsCardinalityService, snackBarService, pdfExportService, treeNodeService, versionHistoryService) {\n    this.diagramApiService = diagramApiService;\n    this.accessService = accessService;\n    this.projectService = projectService;\n    this.propertyService = propertyService;\n    this.diagramUtils = diagramUtils;\n    this.gojsService = gojsService;\n    this.gojsClassService = gojsClassService;\n    this.goJsEnumService = goJsEnumService;\n    this.goJsFolderService = goJsFolderService;\n    this.gojsCommentService = gojsCommentService;\n    this.gojsCommonService = gojsCommonService;\n    this.gojsAttributeService = gojsAttributeService;\n    this.gojsLiteralService = gojsLiteralService;\n    this.gojsCardinalityService = gojsCardinalityService;\n    this.snackBarService = snackBarService;\n    this.pdfExportService = pdfExportService;\n    this.treeNodeService = treeNodeService;\n    this.versionHistoryService = versionHistoryService;\n    this.hasEditAccessOnly = false;\n    this.propertyData = null;\n    this.diagrams = [];\n    this.downloadDiagramEvent = new EventEmitter();\n    this.downloadAllDiagramEvent = new EventEmitter();\n    this.deleteDiagramEvent = new EventEmitter();\n    this.colorSelectionSubject = new BehaviorSubject(false);\n    this.accessService.accessTypeChanges().subscribe(response => {\n      if (response != AccessType.Viewer) {\n        this.hasEditAccessOnly = true;\n      } else {\n        this.hasEditAccessOnly = false;\n      }\n    });\n    this.projectService.currentProjectChanges().subscribe(project => {\n      if (project) {\n        this.project = project;\n        this.diagrams = project.diagrams;\n        const folderDiagrams = this.getDiagramsFromFolders(this.project.folders);\n        this.diagrams.push(...folderDiagrams);\n        // Don't automatically set the first diagram as active here\n        // This will be handled by the ProjectService when opening a project\n        // with a specific diagram ID, or by the DiagramEditorComponent\n        // Just set the current project diagrams\n        this.diagramUtils.setCurrentProjectDiagrams(this.diagrams);\n        // If there are no diagrams, ensure the active diagram is cleared\n        if (this.diagrams.length === 0) {\n          this.diagramUtils.setActiveDiagram(null);\n        }\n      }\n    });\n    this.diagramUtils.activeDiagramChanges().subscribe(diagram => {\n      if (diagram) this.currentDiagram = diagram;\n    });\n    this.propertyService.propertyDataChanges().subscribe(propertyData => {\n      this.propertyData = propertyData;\n    });\n    this.gojsCommonService.gojsDiagramChanges().subscribe(diagram => {\n      if (diagram) this.diagram = diagram;\n    });\n  }\n  getDiagramsFromFolders(folders) {\n    let diagrams = [];\n    for (const folder of folders) {\n      if (folder.diagrams && folder.diagrams.length > 0) {\n        diagrams.push(...folder.diagrams);\n      }\n      if (folder.childFolders && folder.childFolders.length > 0) {\n        diagrams.push(...this.getDiagramsFromFolders(folder.childFolders));\n      }\n    }\n    return diagrams;\n  }\n  setColorSelection(isClicked) {\n    this.colorSelectionSubject.next(isClicked);\n  }\n  getColorSelection() {\n    return this.colorSelectionSubject.asObservable();\n  }\n  triggerCurrentDiagramDownload() {\n    this.downloadDiagramEvent.emit();\n  }\n  triggerAllDiagramDownload(isForOnlyImage) {\n    this.downloadAllDiagramEvent.emit(isForOnlyImage);\n  }\n  triggerDelete() {\n    this.deleteDiagramEvent.emit();\n  }\n  getUpdatedProperties(updatedNode, treeNode) {\n    if (updatedNode && updatedNode.id && updatedNode.category) {\n      if (this.gojsCommonService.isGojsDiagramClassNode(updatedNode)) {\n        if (treeNode == null) treeNode = this.treeNodeService.findNodeByTag(updatedNode.treeNodeTag);\n        if (treeNode) this.gojsClassService.handleClassUpdateInProperty(updatedNode, this.diagram, treeNode);\n      } else if (this.gojsCommonService.isGojsDiagramAttributeNode(updatedNode)) {\n        this.gojsAttributeService.handleAttributeUpdateInProperty(updatedNode, this.diagram, treeNode);\n      } else if (this.gojsCommonService.isGojsDiagramEnumerationNode(updatedNode)) {\n        if (treeNode == null) treeNode = this.treeNodeService.findNodeByTag(updatedNode.treeNodeTag);\n        if (treeNode) this.goJsEnumService.updateEnumNode(updatedNode, this.diagram, treeNode);\n      } else if (this.gojsCommonService.isGojsDiagramLiteralNode(updatedNode)) {\n        this.gojsLiteralService.handleLiteralUpdateInProperty(updatedNode, this.diagram, treeNode);\n      } else if (this.gojsCommonService.isGojsPaletteFolderNode(updatedNode) && treeNode) {\n        this.goJsFolderService.updateFolderNode(updatedNode, treeNode);\n      } else if (this.gojsCommonService.isGojsLinkNode(updatedNode)) {\n        this.gojsCardinalityService.updateLinkFromProperty(updatedNode, this.diagram);\n      }\n    }\n  }\n  /**\n   * Get a Diagram by its id\n   * @param {number} diagramId Id of the Diagram to get.\n   * @returns {Observable<DiagramDetails>} A promise that resolves with the found Diagram or null if no Diagram was found for the given id.\n   * @memberOf DiagramService\n   */\n  getDiagramById(diagramId) {\n    return this.diagramApiService.getDiagramById(diagramId);\n  }\n  /**\n   * Create a new Diagram and returns it.\n   * @param {Diagram} diagram The Diagram to create.\n   * @returns {Observable<Diagram>} The new Diagram.\n   * @memberOf DiagramService\n   */\n  createDiagram(diagram) {\n    return this.diagramApiService.createDiagram(diagram);\n  }\n  /**\n   * Delete a diagram from the server.\n   * @param {number} diagramId ID of the diagram to delete.\n   * @return {Observable<Diagram>}\n   * @memberof DiagramService\n   */\n  deleteDiagram(diagramId) {\n    this.diagramApiService.deleteDiagram(diagramId).subscribe(() => {\n      this.snackBarService.openSnackbar('snackBar.diagramDeleteMsg');\n    });\n  }\n  /**\n   *  Update an existing diagram on the server with the provided data.\n   * @param {*} diagram  Data for updating the diagram.\n   * @returns {Observable<any>}  A promise that resolves when the update is complete.\n   * @memberOf DiagramService\n   */\n  updateDiagram(diagram) {\n    return this.diagramApiService.updateDiagram(diagram);\n  }\n  exportAllDiagrams(projectId) {\n    return this.diagramApiService.exportAllDiagrams(projectId);\n  }\n  /**\n   *  Get the diagram data  from the server and initialize the model of the Diagram content.\n   * @param {number} diagramId is  the id of the selected diagram in the Project detail page\n   * @memberof DiagramEditorComponent\n   */\n  getDiagramDetails(diagramId) {\n    if (this.versionHistoryService.selectedVersion() != null) {\n      const versionHistory = this.versionHistoryService.selectedVersion();\n      if (versionHistory && versionHistory.data != null) {\n        const diagrams = JSON.parse(versionHistory.data).diagrams;\n        const currentDiagram = diagrams.find(dia => dia.id == diagramId);\n        if (currentDiagram) this.getCurrentDiagram(currentDiagram);\n      }\n    } else {\n      this.getDiagramById(diagramId).subscribe(diagram => {\n        this.getCurrentDiagram(diagram);\n      });\n    }\n  }\n  getCurrentDiagram(diagramDetails) {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      if (Object.keys(diagramDetails).length > 0) {\n        const diagramData = yield _this.constructDiagramData(diagramDetails);\n        _this.diagramUtils.setActiveDiagramDetails(diagramData);\n        _this.diagramUtils.initializeDiagramModelData(_this.diagram, diagramData.nodeDataArray, diagramData.linkDataArray);\n        _this.diagram.centerRect(_this.diagram.documentBounds);\n      }\n    })();\n  }\n  constructDiagramData(diagram) {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      const formattedClassData = yield _this2.gojsClassService.formatClassData(diagram.classes, diagram.linkHistories, diagram.id);\n      const diagramEnumNodeData = _this2.goJsEnumService.formatDiagramEnumerationData(diagram.enumerations, _this2.hasEditAccessOnly);\n      const diagramCommentNodeData = _this2.gojsCommentService.formatCommentData(diagram.comments, _this2.hasEditAccessOnly);\n      return {\n        nodeDataArray: [...formattedClassData.nodeDataArray, ...formattedClassData.linkLabelData, ...diagramEnumNodeData, ...diagramCommentNodeData],\n        linkDataArray: formattedClassData.linkDataArray\n      };\n    })();\n  }\n  getPaletteDiagramDetails() {\n    this.gojsService.initPaletteDiagram();\n  }\n  /**\n   * Initiates the download of the current diagram as a PNG image.\n   * @memberOf DiagramEditorComponent\n   */\n  initiateDiagramDownload(isForCurrentDiagram, isForOnlyImg) {\n    try {\n      // Generate image data from the diagram\n      if (isForCurrentDiagram) {\n        if (this.diagram.model.nodeDataArray.length > 0) {\n          this.diagram.commandHandler.zoomToFit();\n          this.diagram.makeImageData({\n            background: 'white',\n            returnType: 'blob',\n            callback: blob => {\n              this.pdfExportService.downloadFile(blob, this.currentDiagram.name, 'png');\n            }\n          });\n        } else {\n          this.snackBarService.info('diagram.downloadNotAllowedMsg');\n        }\n      } else {\n        this.exportAllDiagrams(this.project.id).subscribe(diagrams => {\n          this.downloadAllDiagrams(this.project.id, diagrams, isForOnlyImg);\n        });\n      }\n    } catch (error) {\n      console.error('Failed to initiate diagram download:', error);\n    }\n  }\n  downloadAllDiagrams(projectId, diagrams, isForOnlyImage) {\n    var _this3 = this;\n    return _asyncToGenerator(function* () {\n      const treeNodes = _this3.treeNodeService.descendantTreeNodes();\n      if (!treeNodes || treeNodes.length === 0) {\n        _this3.snackBarService.info('diagram.downloadNotAllowedMsg');\n        return;\n      }\n      const diagramBlobs = (yield Promise.all(treeNodes.filter(node => node.category === GojsNodeCategory.Diagram).map( /*#__PURE__*/function () {\n        var _ref = _asyncToGenerator(function* (node) {\n          const diagram = diagrams.find(dia => dia.id === node.data?.id);\n          if (diagram && (diagram.classes.length > 0 || diagram.enumerations.length > 0 || diagram.comments.length > 0)) {\n            const imageData = yield _this3.generateImageFromDiagram(diagram);\n            return {\n              diagramId: diagram.id,\n              image: imageData,\n              name: diagram.name\n            };\n          }\n          return null;\n        });\n        return function (_x) {\n          return _ref.apply(this, arguments);\n        };\n      }()))).filter(blob => blob !== null);\n      if (diagramBlobs.length > 0) {\n        _this3.pdfExportService.downloadPdf(projectId, _this3.project.name, {\n          projectId,\n          diagrams: diagramBlobs\n        }, isForOnlyImage);\n        const diagram = diagrams.find(diagram => diagram.id == _this3.currentDiagram.id);\n        if (diagram) {\n          const diagramData = yield _this3.constructDiagramData(diagram);\n          _this3.diagramUtils.initializeDiagramModelData(_this3.diagram, diagramData.nodeDataArray, diagramData.linkDataArray);\n        }\n      } else {\n        _this3.snackBarService.info('diagram.downloadNotAllowedMsg');\n      }\n    })();\n  }\n  generateImageFromDiagram(diagram) {\n    var _this4 = this;\n    return _asyncToGenerator(function* () {\n      const diagramData = yield _this4.constructDiagramData(diagram);\n      return new Promise(resolve => {\n        const div = document.getElementById('diagramDiv');\n        if (!div) return;\n        const tempDiagram = go.Diagram.fromDiv(div);\n        if (!tempDiagram) return;\n        _this4.diagramUtils.initializeDiagramModelData(tempDiagram, diagramData.nodeDataArray, diagramData.linkDataArray);\n        tempDiagram.commandHandler.zoomToFit();\n        tempDiagram.makeImageData({\n          background: 'white',\n          callback: blob => {\n            if (blob) {\n              resolve(blob);\n            }\n          },\n          returnType: 'file'\n        });\n      });\n    })();\n  }\n  static #_ = this.ɵfac = function DiagramService_Factory(t) {\n    return new (t || DiagramService)(i0.ɵɵinject(i1.DiagramApiService), i0.ɵɵinject(i2.AccessService), i0.ɵɵinject(i3.ProjectService), i0.ɵɵinject(i4.PropertyService), i0.ɵɵinject(i5.DiagramUtils), i0.ɵɵinject(i6.GojsService), i0.ɵɵinject(i7.GojsClassService), i0.ɵɵinject(i8.GojsEnumerationService), i0.ɵɵinject(i9.GojsFolderService), i0.ɵɵinject(i10.GojsCommentService), i0.ɵɵinject(i11.GojsCommonService), i0.ɵɵinject(i12.GojsAttributeService), i0.ɵɵinject(i13.GojsLiteralService), i0.ɵɵinject(i14.GojsCardinalityService), i0.ɵɵinject(i15.SnackBarService), i0.ɵɵinject(i16.PdfExportService), i0.ɵɵinject(i17.TreeNodeService), i0.ɵɵinject(i18.VersionHistoryService));\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: DiagramService,\n    factory: DiagramService.ɵfac,\n    providedIn: 'root'\n  });\n}", "map": {"version": 3, "names": ["EventEmitter", "go", "BehaviorSubject", "GojsNodeCategory", "AccessType", "DiagramService", "constructor", "diagramApiService", "accessService", "projectService", "propertyService", "diagramUtils", "gojsService", "gojsClassService", "goJsEnumService", "goJsFolderService", "gojsCommentService", "gojsCommonService", "gojsAttributeService", "gojsLiteralService", "gojsCardinalityService", "snackBarService", "pdfExportService", "treeNodeService", "versionHistoryService", "hasEditAccessOnly", "propertyData", "diagrams", "downloadDiagramEvent", "downloadAllDiagramEvent", "deleteDiagramEvent", "colorSelectionSubject", "accessTypeChanges", "subscribe", "response", "Viewer", "currentProjectChanges", "project", "folderDiagrams", "getDiagramsFromFolders", "folders", "push", "setCurrentProjectDiagrams", "length", "setActiveDiagram", "activeDiagramChanges", "diagram", "currentDiagram", "propertyDataChanges", "gojsDiagramChanges", "folder", "childFolders", "setColorSelection", "isClicked", "next", "getColorSelection", "asObservable", "triggerCurrentDiagramDownload", "emit", "triggerAllDiagramDownload", "isForOnlyImage", "triggerDelete", "getUpdatedProperties", "updatedNode", "treeNode", "id", "category", "isGojsDiagramClassNode", "findNodeByTag", "treeNodeTag", "handleClassUpdateInProperty", "isGojsDiagramAttributeNode", "handleAttributeUpdateInProperty", "isGojsDiagramEnumerationNode", "updateEnumNode", "isGojsDiagramLiteralNode", "handleLiteralUpdateInProperty", "isGojsPaletteFolderNode", "updateFolderNode", "isGojsLinkNode", "updateLinkFromProperty", "getDiagramById", "diagramId", "createDiagram", "deleteDiagram", "openSnackbar", "updateDiagram", "exportAllDiagrams", "projectId", "getDiagramDetails", "selectedVersion", "versionHistory", "data", "JSON", "parse", "find", "dia", "getCurrentDiagram", "diagramDetails", "_this", "_asyncToGenerator", "Object", "keys", "diagramData", "constructDiagramData", "setActiveDiagramDetails", "initializeDiagramModelData", "nodeDataArray", "linkDataArray", "centerRect", "documentBounds", "_this2", "formattedClassData", "formatClassData", "classes", "linkHistories", "diagramEnumNodeData", "formatDiagramEnumerationData", "enumerations", "diagramCommentNodeData", "formatCommentData", "comments", "linkLabelData", "getPaletteDiagramDetails", "initPaletteDiagram", "initiateDiagramDownload", "isForCurrentDiagram", "isForOnlyImg", "model", "command<PERSON><PERSON>ler", "zoomToFit", "makeImageData", "background", "returnType", "callback", "blob", "downloadFile", "name", "info", "downloadAllDiagrams", "error", "console", "_this3", "treeNodes", "descendantTreeNodes", "diagramBlobs", "Promise", "all", "filter", "node", "Diagram", "map", "_ref", "imageData", "generateImageFromDiagram", "image", "_x", "apply", "arguments", "downloadPdf", "_this4", "resolve", "div", "document", "getElementById", "tempDiagram", "fromDiv", "_", "i0", "ɵɵinject", "i1", "DiagramApiService", "i2", "AccessService", "i3", "ProjectService", "i4", "PropertyService", "i5", "DiagramUtils", "i6", "GojsService", "i7", "GojsClassService", "i8", "GojsEnumerationService", "i9", "GojsFolderService", "i10", "GojsCommentService", "i11", "GojsCommonService", "i12", "GojsAttributeService", "i13", "GojsLiteralService", "i14", "GojsCardinalityService", "i15", "SnackBarService", "i16", "PdfExportService", "i17", "TreeNodeService", "i18", "VersionHistoryService", "_2", "factory", "ɵfac", "providedIn"], "sources": ["D:\\GitHub\\Bassetti\\devint-BASSETTI-GROUP-APP\\BassettiUMLWebApp\\UMLApp\\src\\app\\core\\services\\diagram\\diagram.service.ts"], "sourcesContent": ["import { EventEmitter, Injectable } from '@angular/core';\r\nimport * as go from 'gojs';\r\nimport { BehaviorSubject, Observable } from 'rxjs';\r\nimport { FolderDTO } from 'src/app/shared/model/class';\r\nimport { ExportDiagram } from 'src/app/shared/model/common';\r\nimport {\r\n  Diagram,\r\n  DiagramDetails,\r\n  DiagramUpdateDTO,\r\n} from 'src/app/shared/model/diagram';\r\nimport {\r\n  GojsDiagramAttributeNode,\r\n  GojsDiagramClassNode,\r\n  GojsDiagramEnumerationNode,\r\n  GojsDiagramLiteralNode,\r\n  GojsFolderNode,\r\n  GojsLinkNode,\r\n  GojsNodeCategory,\r\n} from 'src/app/shared/model/gojs';\r\nimport { AccessType, ProjectDetails } from 'src/app/shared/model/project';\r\nimport { TreeNode } from 'src/app/shared/model/treeNode';\r\nimport { DiagramUtils } from 'src/app/shared/utils/diagram-utils';\r\nimport { AccessService } from '../access/access.service';\r\nimport { DiagramApiService } from '../api/diagram-api.service';\r\nimport { GojsCommentService } from '../gojs/gojs-comment/gojs-comment.service';\r\nimport { GojsService } from '../gojs/gojs.service';\r\nimport { GojsAttributeService } from '../gojs/gojsAttribute/gojs-attribute.service';\r\nimport { GojsCardinalityService } from '../gojs/gojsCardinality/gojs-cardinality.service';\r\nimport { GojsClassService } from '../gojs/gojsClass/gojs-class.service';\r\nimport { GojsCommonService } from '../gojs/gojsCommon/gojs-common.service';\r\nimport { GojsEnumerationService } from '../gojs/gojsEnumeration/gojs-enumeration.service';\r\nimport { GojsFolderService } from '../gojs/gojsFolder/gojs-folder.service';\r\nimport { GojsLiteralService } from '../gojs/gojsLiteral/gojs-literal.service';\r\nimport { PdfExportService } from '../pdf-export/pdf-export.service';\r\nimport { ProjectService } from '../project/project.service';\r\nimport { PropertyService } from '../property/property.service';\r\nimport { SnackBarService } from '../snackbar/snack-bar.service';\r\nimport { TreeNodeService } from '../treeNode/tree-node.service';\r\nimport { VersionHistoryService } from '../versionHistory/version-history.service';\r\n@Injectable({\r\n  providedIn: 'root',\r\n})\r\nexport class DiagramService {\r\n  private diagram!: go.Diagram;\r\n  private hasEditAccessOnly: boolean = false;\r\n  currentDiagram!: Diagram;\r\n  propertyData:\r\n    | GojsDiagramClassNode\r\n    | GojsDiagramEnumerationNode\r\n    | GojsDiagramAttributeNode\r\n    | GojsDiagramLiteralNode\r\n    | GojsFolderNode\r\n    | GojsLinkNode\r\n    | null = null;\r\n  project!: ProjectDetails;\r\n  diagrams: Diagram[] = [];\r\n  downloadDiagramEvent = new EventEmitter<void>();\r\n  downloadAllDiagramEvent = new EventEmitter<boolean>();\r\n  deleteDiagramEvent = new EventEmitter<void>();\r\n\r\n  private colorSelectionSubject = new BehaviorSubject<boolean>(false);\r\n  constructor(\r\n    private diagramApiService: DiagramApiService,\r\n    private accessService: AccessService,\r\n    private projectService: ProjectService,\r\n    private propertyService: PropertyService,\r\n    private diagramUtils: DiagramUtils,\r\n    private gojsService: GojsService,\r\n    private gojsClassService: GojsClassService,\r\n    private goJsEnumService: GojsEnumerationService,\r\n    private goJsFolderService: GojsFolderService,\r\n    private gojsCommentService: GojsCommentService,\r\n    private gojsCommonService: GojsCommonService,\r\n    private gojsAttributeService: GojsAttributeService,\r\n    private gojsLiteralService: GojsLiteralService,\r\n    private gojsCardinalityService: GojsCardinalityService,\r\n    private snackBarService: SnackBarService,\r\n    private pdfExportService: PdfExportService,\r\n    private treeNodeService: TreeNodeService,\r\n    private versionHistoryService: VersionHistoryService\r\n  ) {\r\n    this.accessService.accessTypeChanges().subscribe((response) => {\r\n      if (response != AccessType.Viewer) {\r\n        this.hasEditAccessOnly = true;\r\n      } else {\r\n        this.hasEditAccessOnly = false;\r\n      }\r\n    });\r\n    this.projectService.currentProjectChanges().subscribe((project) => {\r\n      if (project) {\r\n        this.project = project;\r\n        this.diagrams = project.diagrams;\r\n        const folderDiagrams = this.getDiagramsFromFolders(\r\n          this.project.folders\r\n        );\r\n        this.diagrams.push(...folderDiagrams);\r\n\r\n        // Don't automatically set the first diagram as active here\r\n        // This will be handled by the ProjectService when opening a project\r\n        // with a specific diagram ID, or by the DiagramEditorComponent\r\n\r\n        // Just set the current project diagrams\r\n        this.diagramUtils.setCurrentProjectDiagrams(this.diagrams);\r\n\r\n        // If there are no diagrams, ensure the active diagram is cleared\r\n        if (this.diagrams.length === 0) {\r\n          this.diagramUtils.setActiveDiagram(null);\r\n        }\r\n      }\r\n    });\r\n\r\n    this.diagramUtils.activeDiagramChanges().subscribe((diagram) => {\r\n      if (diagram) this.currentDiagram = diagram;\r\n    });\r\n\r\n    this.propertyService.propertyDataChanges().subscribe((propertyData) => {\r\n      this.propertyData = propertyData;\r\n    });\r\n    this.gojsCommonService.gojsDiagramChanges().subscribe((diagram) => {\r\n      if (diagram) this.diagram = diagram;\r\n    });\r\n  }\r\n\r\n  getDiagramsFromFolders(folders: FolderDTO[]): Diagram[] {\r\n    let diagrams: Diagram[] = [];\r\n    for (const folder of folders) {\r\n      if (folder.diagrams && folder.diagrams.length > 0) {\r\n        diagrams.push(...folder.diagrams);\r\n      }\r\n      if (folder.childFolders && folder.childFolders.length > 0) {\r\n        diagrams.push(...this.getDiagramsFromFolders(folder.childFolders));\r\n      }\r\n    }\r\n    return diagrams;\r\n  }\r\n\r\n  setColorSelection(isClicked: boolean) {\r\n    this.colorSelectionSubject.next(isClicked);\r\n  }\r\n\r\n  getColorSelection() {\r\n    return this.colorSelectionSubject.asObservable();\r\n  }\r\n  triggerCurrentDiagramDownload() {\r\n    this.downloadDiagramEvent.emit();\r\n  }\r\n\r\n  triggerAllDiagramDownload(isForOnlyImage: boolean) {\r\n    this.downloadAllDiagramEvent.emit(isForOnlyImage);\r\n  }\r\n\r\n  triggerDelete() {\r\n    this.deleteDiagramEvent.emit();\r\n  }\r\n\r\n  getUpdatedProperties(\r\n    updatedNode:\r\n      | GojsDiagramClassNode\r\n      | GojsDiagramEnumerationNode\r\n      | GojsDiagramAttributeNode\r\n      | GojsDiagramLiteralNode\r\n      | GojsLinkNode\r\n      | GojsFolderNode,\r\n    treeNode: TreeNode | null\r\n  ): void {\r\n    if (updatedNode && updatedNode.id && updatedNode.category) {\r\n      if (this.gojsCommonService.isGojsDiagramClassNode(updatedNode)) {\r\n        if (treeNode == null)\r\n          treeNode = this.treeNodeService.findNodeByTag(\r\n            updatedNode.treeNodeTag\r\n          );\r\n        if (treeNode)\r\n          this.gojsClassService.handleClassUpdateInProperty(\r\n            updatedNode,\r\n            this.diagram,\r\n            treeNode\r\n          );\r\n      } else if (\r\n        this.gojsCommonService.isGojsDiagramAttributeNode(updatedNode)\r\n      ) {\r\n        this.gojsAttributeService.handleAttributeUpdateInProperty(\r\n          updatedNode,\r\n          this.diagram,\r\n          treeNode\r\n        );\r\n      } else if (\r\n        this.gojsCommonService.isGojsDiagramEnumerationNode(updatedNode)\r\n      ) {\r\n        if (treeNode == null)\r\n          treeNode = this.treeNodeService.findNodeByTag(\r\n            updatedNode.treeNodeTag\r\n          );\r\n        if (treeNode)\r\n          this.goJsEnumService.updateEnumNode(\r\n            updatedNode,\r\n            this.diagram,\r\n            treeNode\r\n          );\r\n      } else if (this.gojsCommonService.isGojsDiagramLiteralNode(updatedNode)) {\r\n        this.gojsLiteralService.handleLiteralUpdateInProperty(\r\n          updatedNode,\r\n          this.diagram,\r\n          treeNode\r\n        );\r\n      } else if (\r\n        this.gojsCommonService.isGojsPaletteFolderNode(updatedNode) &&\r\n        treeNode\r\n      ) {\r\n        this.goJsFolderService.updateFolderNode(updatedNode, treeNode);\r\n      } else if (this.gojsCommonService.isGojsLinkNode(updatedNode)) {\r\n        this.gojsCardinalityService.updateLinkFromProperty(\r\n          updatedNode,\r\n          this.diagram\r\n        );\r\n      }\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get a Diagram by its id\r\n   * @param {number} diagramId Id of the Diagram to get.\r\n   * @returns {Observable<DiagramDetails>} A promise that resolves with the found Diagram or null if no Diagram was found for the given id.\r\n   * @memberOf DiagramService\r\n   */\r\n  getDiagramById(diagramId: number): Observable<DiagramDetails> {\r\n    return this.diagramApiService.getDiagramById(diagramId);\r\n  }\r\n\r\n  /**\r\n   * Create a new Diagram and returns it.\r\n   * @param {Diagram} diagram The Diagram to create.\r\n   * @returns {Observable<Diagram>} The new Diagram.\r\n   * @memberOf DiagramService\r\n   */\r\n  createDiagram(diagram: Diagram): Observable<Diagram> {\r\n    return this.diagramApiService.createDiagram(diagram);\r\n  }\r\n\r\n  /**\r\n   * Delete a diagram from the server.\r\n   * @param {number} diagramId ID of the diagram to delete.\r\n   * @return {Observable<Diagram>}\r\n   * @memberof DiagramService\r\n   */\r\n  deleteDiagram(diagramId: number): void {\r\n    this.diagramApiService.deleteDiagram(diagramId).subscribe(() => {\r\n      this.snackBarService.openSnackbar('snackBar.diagramDeleteMsg');\r\n    });\r\n  }\r\n\r\n  /**\r\n   *  Update an existing diagram on the server with the provided data.\r\n   * @param {*} diagram  Data for updating the diagram.\r\n   * @returns {Observable<any>}  A promise that resolves when the update is complete.\r\n   * @memberOf DiagramService\r\n   */\r\n  updateDiagram(diagram: DiagramUpdateDTO): Observable<DiagramUpdateDTO> {\r\n    return this.diagramApiService.updateDiagram(diagram);\r\n  }\r\n\r\n  exportAllDiagrams(projectId: number): Observable<DiagramDetails[]> {\r\n    return this.diagramApiService.exportAllDiagrams(projectId);\r\n  }\r\n  /**\r\n   *  Get the diagram data  from the server and initialize the model of the Diagram content.\r\n   * @param {number} diagramId is  the id of the selected diagram in the Project detail page\r\n   * @memberof DiagramEditorComponent\r\n   */\r\n  public getDiagramDetails(diagramId: number) {\r\n    if (this.versionHistoryService.selectedVersion() != null) {\r\n      const versionHistory = this.versionHistoryService.selectedVersion();\r\n      if (versionHistory && versionHistory.data != null) {\r\n        const diagrams: DiagramDetails[] = JSON.parse(\r\n          versionHistory.data\r\n        ).diagrams;\r\n        const currentDiagram = diagrams.find(\r\n          (dia: DiagramDetails) => dia.id == diagramId\r\n        );\r\n        if (currentDiagram) this.getCurrentDiagram(currentDiagram);\r\n      }\r\n    } else {\r\n      this.getDiagramById(diagramId).subscribe((diagram) => {\r\n        this.getCurrentDiagram(diagram);\r\n      });\r\n    }\r\n  }\r\n\r\n  private async getCurrentDiagram(diagramDetails: DiagramDetails) {\r\n    if (Object.keys(diagramDetails).length > 0) {\r\n      const diagramData = await this.constructDiagramData(diagramDetails);\r\n      this.diagramUtils.setActiveDiagramDetails(diagramData);\r\n      this.diagramUtils.initializeDiagramModelData(\r\n        this.diagram,\r\n        diagramData.nodeDataArray,\r\n        diagramData.linkDataArray\r\n      );\r\n      this.diagram.centerRect(this.diagram.documentBounds);\r\n    }\r\n  }\r\n\r\n  async constructDiagramData(diagram: DiagramDetails) {\r\n    const formattedClassData = await this.gojsClassService.formatClassData(\r\n      diagram.classes,\r\n      diagram.linkHistories,\r\n      diagram.id!\r\n    );\r\n\r\n    const diagramEnumNodeData =\r\n      this.goJsEnumService.formatDiagramEnumerationData(\r\n        diagram.enumerations,\r\n        this.hasEditAccessOnly\r\n      );\r\n    const diagramCommentNodeData = this.gojsCommentService.formatCommentData(\r\n      diagram.comments,\r\n      this.hasEditAccessOnly\r\n    );\r\n\r\n    return {\r\n      nodeDataArray: [\r\n        ...formattedClassData.nodeDataArray,\r\n        ...formattedClassData.linkLabelData,\r\n        ...diagramEnumNodeData,\r\n        ...diagramCommentNodeData,\r\n      ],\r\n      linkDataArray: formattedClassData.linkDataArray,\r\n    };\r\n  }\r\n\r\n  getPaletteDiagramDetails() {\r\n    this.gojsService.initPaletteDiagram();\r\n  }\r\n\r\n  /**\r\n   * Initiates the download of the current diagram as a PNG image.\r\n   * @memberOf DiagramEditorComponent\r\n   */\r\n  initiateDiagramDownload(isForCurrentDiagram: boolean, isForOnlyImg: boolean) {\r\n    try {\r\n      // Generate image data from the diagram\r\n      if (isForCurrentDiagram) {\r\n        if (this.diagram.model.nodeDataArray.length > 0) {\r\n          this.diagram.commandHandler.zoomToFit();\r\n          this.diagram.makeImageData({\r\n            background: 'white',\r\n            returnType: 'blob',\r\n            callback: (blob: Blob) => {\r\n              this.pdfExportService.downloadFile(\r\n                blob,\r\n                this.currentDiagram.name,\r\n                'png'\r\n              );\r\n            },\r\n          });\r\n        } else {\r\n          this.snackBarService.info('diagram.downloadNotAllowedMsg');\r\n        }\r\n      } else {\r\n        this.exportAllDiagrams(this.project.id!).subscribe((diagrams) => {\r\n          this.downloadAllDiagrams(this.project.id!, diagrams, isForOnlyImg);\r\n        });\r\n      }\r\n    } catch (error) {\r\n      console.error('Failed to initiate diagram download:', error);\r\n    }\r\n  }\r\n\r\n  private async downloadAllDiagrams(\r\n    projectId: number,\r\n    diagrams: DiagramDetails[],\r\n    isForOnlyImage: boolean\r\n  ): Promise<void> {\r\n    const treeNodes = this.treeNodeService.descendantTreeNodes();\r\n    if (!treeNodes || treeNodes.length === 0) {\r\n      this.snackBarService.info('diagram.downloadNotAllowedMsg');\r\n      return;\r\n    }\r\n\r\n    const diagramBlobs = (\r\n      await Promise.all(\r\n        treeNodes\r\n          .filter((node) => node.category === GojsNodeCategory.Diagram)\r\n          .map(async (node) => {\r\n            const diagram = diagrams.find((dia) => dia.id === node.data?.id);\r\n            if (\r\n              diagram &&\r\n              (diagram.classes.length > 0 ||\r\n                diagram.enumerations.length > 0 ||\r\n                diagram.comments.length > 0)\r\n            ) {\r\n              const imageData = await this.generateImageFromDiagram(diagram);\r\n              return {\r\n                diagramId: diagram.id!,\r\n                image: imageData,\r\n                name: diagram.name,\r\n              };\r\n            }\r\n            return null;\r\n          })\r\n      )\r\n    ).filter((blob): blob is ExportDiagram => blob !== null);\r\n\r\n    if (diagramBlobs.length > 0) {\r\n      this.pdfExportService.downloadPdf(\r\n        projectId,\r\n        this.project.name,\r\n        {\r\n          projectId,\r\n          diagrams: diagramBlobs,\r\n        },\r\n        isForOnlyImage\r\n      );\r\n      const diagram = diagrams.find(\r\n        (diagram) => diagram.id == this.currentDiagram.id\r\n      );\r\n      if (diagram) {\r\n        const diagramData = await this.constructDiagramData(diagram);\r\n        this.diagramUtils.initializeDiagramModelData(\r\n          this.diagram,\r\n          diagramData.nodeDataArray,\r\n          diagramData.linkDataArray\r\n        );\r\n      }\r\n    } else {\r\n      this.snackBarService.info('diagram.downloadNotAllowedMsg');\r\n    }\r\n  }\r\n\r\n  private async generateImageFromDiagram(\r\n    diagram: DiagramDetails\r\n  ): Promise<string> {\r\n    const diagramData = await this.constructDiagramData(diagram);\r\n\r\n    return new Promise((resolve) => {\r\n      const div = document.getElementById('diagramDiv');\r\n      if (!div) return;\r\n\r\n      const tempDiagram = go.Diagram.fromDiv(div);\r\n      if (!tempDiagram) return;\r\n\r\n      this.diagramUtils.initializeDiagramModelData(\r\n        tempDiagram,\r\n        diagramData.nodeDataArray,\r\n        diagramData.linkDataArray\r\n      );\r\n      tempDiagram.commandHandler.zoomToFit();\r\n\r\n      tempDiagram.makeImageData({\r\n        background: 'white',\r\n        callback: (blob) => {\r\n          if (blob) {\r\n            resolve(blob);\r\n          }\r\n        },\r\n        returnType: 'file',\r\n      });\r\n    });\r\n  }\r\n}\r\n"], "mappings": ";AAAA,SAASA,YAAY,QAAoB,eAAe;AACxD,OAAO,KAAKC,EAAE,MAAM,MAAM;AAC1B,SAASC,eAAe,QAAoB,MAAM;AAQlD,SAOEC,gBAAgB,QACX,2BAA2B;AAClC,SAASC,UAAU,QAAwB,8BAA8B;;;;;;;;;;;;;;;;;;;;AAuBzE,OAAM,MAAOC,cAAc;EAmBzBC,YACUC,iBAAoC,EACpCC,aAA4B,EAC5BC,cAA8B,EAC9BC,eAAgC,EAChCC,YAA0B,EAC1BC,WAAwB,EACxBC,gBAAkC,EAClCC,eAAuC,EACvCC,iBAAoC,EACpCC,kBAAsC,EACtCC,iBAAoC,EACpCC,oBAA0C,EAC1CC,kBAAsC,EACtCC,sBAA8C,EAC9CC,eAAgC,EAChCC,gBAAkC,EAClCC,eAAgC,EAChCC,qBAA4C;IAjB5C,KAAAjB,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,YAAY,GAAZA,YAAY;IACZ,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,kBAAkB,GAAlBA,kBAAkB;IAClB,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,oBAAoB,GAApBA,oBAAoB;IACpB,KAAAC,kBAAkB,GAAlBA,kBAAkB;IAClB,KAAAC,sBAAsB,GAAtBA,sBAAsB;IACtB,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,qBAAqB,GAArBA,qBAAqB;IAnCvB,KAAAC,iBAAiB,GAAY,KAAK;IAE1C,KAAAC,YAAY,GAOD,IAAI;IAEf,KAAAC,QAAQ,GAAc,EAAE;IACxB,KAAAC,oBAAoB,GAAG,IAAI5B,YAAY,EAAQ;IAC/C,KAAA6B,uBAAuB,GAAG,IAAI7B,YAAY,EAAW;IACrD,KAAA8B,kBAAkB,GAAG,IAAI9B,YAAY,EAAQ;IAErC,KAAA+B,qBAAqB,GAAG,IAAI7B,eAAe,CAAU,KAAK,CAAC;IAqBjE,IAAI,CAACM,aAAa,CAACwB,iBAAiB,EAAE,CAACC,SAAS,CAAEC,QAAQ,IAAI;MAC5D,IAAIA,QAAQ,IAAI9B,UAAU,CAAC+B,MAAM,EAAE;QACjC,IAAI,CAACV,iBAAiB,GAAG,IAAI;OAC9B,MAAM;QACL,IAAI,CAACA,iBAAiB,GAAG,KAAK;;IAElC,CAAC,CAAC;IACF,IAAI,CAAChB,cAAc,CAAC2B,qBAAqB,EAAE,CAACH,SAAS,CAAEI,OAAO,IAAI;MAChE,IAAIA,OAAO,EAAE;QACX,IAAI,CAACA,OAAO,GAAGA,OAAO;QACtB,IAAI,CAACV,QAAQ,GAAGU,OAAO,CAACV,QAAQ;QAChC,MAAMW,cAAc,GAAG,IAAI,CAACC,sBAAsB,CAChD,IAAI,CAACF,OAAO,CAACG,OAAO,CACrB;QACD,IAAI,CAACb,QAAQ,CAACc,IAAI,CAAC,GAAGH,cAAc,CAAC;QAErC;QACA;QACA;QAEA;QACA,IAAI,CAAC3B,YAAY,CAAC+B,yBAAyB,CAAC,IAAI,CAACf,QAAQ,CAAC;QAE1D;QACA,IAAI,IAAI,CAACA,QAAQ,CAACgB,MAAM,KAAK,CAAC,EAAE;UAC9B,IAAI,CAAChC,YAAY,CAACiC,gBAAgB,CAAC,IAAI,CAAC;;;IAG9C,CAAC,CAAC;IAEF,IAAI,CAACjC,YAAY,CAACkC,oBAAoB,EAAE,CAACZ,SAAS,CAAEa,OAAO,IAAI;MAC7D,IAAIA,OAAO,EAAE,IAAI,CAACC,cAAc,GAAGD,OAAO;IAC5C,CAAC,CAAC;IAEF,IAAI,CAACpC,eAAe,CAACsC,mBAAmB,EAAE,CAACf,SAAS,CAAEP,YAAY,IAAI;MACpE,IAAI,CAACA,YAAY,GAAGA,YAAY;IAClC,CAAC,CAAC;IACF,IAAI,CAACT,iBAAiB,CAACgC,kBAAkB,EAAE,CAAChB,SAAS,CAAEa,OAAO,IAAI;MAChE,IAAIA,OAAO,EAAE,IAAI,CAACA,OAAO,GAAGA,OAAO;IACrC,CAAC,CAAC;EACJ;EAEAP,sBAAsBA,CAACC,OAAoB;IACzC,IAAIb,QAAQ,GAAc,EAAE;IAC5B,KAAK,MAAMuB,MAAM,IAAIV,OAAO,EAAE;MAC5B,IAAIU,MAAM,CAACvB,QAAQ,IAAIuB,MAAM,CAACvB,QAAQ,CAACgB,MAAM,GAAG,CAAC,EAAE;QACjDhB,QAAQ,CAACc,IAAI,CAAC,GAAGS,MAAM,CAACvB,QAAQ,CAAC;;MAEnC,IAAIuB,MAAM,CAACC,YAAY,IAAID,MAAM,CAACC,YAAY,CAACR,MAAM,GAAG,CAAC,EAAE;QACzDhB,QAAQ,CAACc,IAAI,CAAC,GAAG,IAAI,CAACF,sBAAsB,CAACW,MAAM,CAACC,YAAY,CAAC,CAAC;;;IAGtE,OAAOxB,QAAQ;EACjB;EAEAyB,iBAAiBA,CAACC,SAAkB;IAClC,IAAI,CAACtB,qBAAqB,CAACuB,IAAI,CAACD,SAAS,CAAC;EAC5C;EAEAE,iBAAiBA,CAAA;IACf,OAAO,IAAI,CAACxB,qBAAqB,CAACyB,YAAY,EAAE;EAClD;EACAC,6BAA6BA,CAAA;IAC3B,IAAI,CAAC7B,oBAAoB,CAAC8B,IAAI,EAAE;EAClC;EAEAC,yBAAyBA,CAACC,cAAuB;IAC/C,IAAI,CAAC/B,uBAAuB,CAAC6B,IAAI,CAACE,cAAc,CAAC;EACnD;EAEAC,aAAaA,CAAA;IACX,IAAI,CAAC/B,kBAAkB,CAAC4B,IAAI,EAAE;EAChC;EAEAI,oBAAoBA,CAClBC,WAMkB,EAClBC,QAAyB;IAEzB,IAAID,WAAW,IAAIA,WAAW,CAACE,EAAE,IAAIF,WAAW,CAACG,QAAQ,EAAE;MACzD,IAAI,IAAI,CAACjD,iBAAiB,CAACkD,sBAAsB,CAACJ,WAAW,CAAC,EAAE;QAC9D,IAAIC,QAAQ,IAAI,IAAI,EAClBA,QAAQ,GAAG,IAAI,CAACzC,eAAe,CAAC6C,aAAa,CAC3CL,WAAW,CAACM,WAAW,CACxB;QACH,IAAIL,QAAQ,EACV,IAAI,CAACnD,gBAAgB,CAACyD,2BAA2B,CAC/CP,WAAW,EACX,IAAI,CAACjB,OAAO,EACZkB,QAAQ,CACT;OACJ,MAAM,IACL,IAAI,CAAC/C,iBAAiB,CAACsD,0BAA0B,CAACR,WAAW,CAAC,EAC9D;QACA,IAAI,CAAC7C,oBAAoB,CAACsD,+BAA+B,CACvDT,WAAW,EACX,IAAI,CAACjB,OAAO,EACZkB,QAAQ,CACT;OACF,MAAM,IACL,IAAI,CAAC/C,iBAAiB,CAACwD,4BAA4B,CAACV,WAAW,CAAC,EAChE;QACA,IAAIC,QAAQ,IAAI,IAAI,EAClBA,QAAQ,GAAG,IAAI,CAACzC,eAAe,CAAC6C,aAAa,CAC3CL,WAAW,CAACM,WAAW,CACxB;QACH,IAAIL,QAAQ,EACV,IAAI,CAAClD,eAAe,CAAC4D,cAAc,CACjCX,WAAW,EACX,IAAI,CAACjB,OAAO,EACZkB,QAAQ,CACT;OACJ,MAAM,IAAI,IAAI,CAAC/C,iBAAiB,CAAC0D,wBAAwB,CAACZ,WAAW,CAAC,EAAE;QACvE,IAAI,CAAC5C,kBAAkB,CAACyD,6BAA6B,CACnDb,WAAW,EACX,IAAI,CAACjB,OAAO,EACZkB,QAAQ,CACT;OACF,MAAM,IACL,IAAI,CAAC/C,iBAAiB,CAAC4D,uBAAuB,CAACd,WAAW,CAAC,IAC3DC,QAAQ,EACR;QACA,IAAI,CAACjD,iBAAiB,CAAC+D,gBAAgB,CAACf,WAAW,EAAEC,QAAQ,CAAC;OAC/D,MAAM,IAAI,IAAI,CAAC/C,iBAAiB,CAAC8D,cAAc,CAAChB,WAAW,CAAC,EAAE;QAC7D,IAAI,CAAC3C,sBAAsB,CAAC4D,sBAAsB,CAChDjB,WAAW,EACX,IAAI,CAACjB,OAAO,CACb;;;EAGP;EAEA;;;;;;EAMAmC,cAAcA,CAACC,SAAiB;IAC9B,OAAO,IAAI,CAAC3E,iBAAiB,CAAC0E,cAAc,CAACC,SAAS,CAAC;EACzD;EAEA;;;;;;EAMAC,aAAaA,CAACrC,OAAgB;IAC5B,OAAO,IAAI,CAACvC,iBAAiB,CAAC4E,aAAa,CAACrC,OAAO,CAAC;EACtD;EAEA;;;;;;EAMAsC,aAAaA,CAACF,SAAiB;IAC7B,IAAI,CAAC3E,iBAAiB,CAAC6E,aAAa,CAACF,SAAS,CAAC,CAACjD,SAAS,CAAC,MAAK;MAC7D,IAAI,CAACZ,eAAe,CAACgE,YAAY,CAAC,2BAA2B,CAAC;IAChE,CAAC,CAAC;EACJ;EAEA;;;;;;EAMAC,aAAaA,CAACxC,OAAyB;IACrC,OAAO,IAAI,CAACvC,iBAAiB,CAAC+E,aAAa,CAACxC,OAAO,CAAC;EACtD;EAEAyC,iBAAiBA,CAACC,SAAiB;IACjC,OAAO,IAAI,CAACjF,iBAAiB,CAACgF,iBAAiB,CAACC,SAAS,CAAC;EAC5D;EACA;;;;;EAKOC,iBAAiBA,CAACP,SAAiB;IACxC,IAAI,IAAI,CAAC1D,qBAAqB,CAACkE,eAAe,EAAE,IAAI,IAAI,EAAE;MACxD,MAAMC,cAAc,GAAG,IAAI,CAACnE,qBAAqB,CAACkE,eAAe,EAAE;MACnE,IAAIC,cAAc,IAAIA,cAAc,CAACC,IAAI,IAAI,IAAI,EAAE;QACjD,MAAMjE,QAAQ,GAAqBkE,IAAI,CAACC,KAAK,CAC3CH,cAAc,CAACC,IAAI,CACpB,CAACjE,QAAQ;QACV,MAAMoB,cAAc,GAAGpB,QAAQ,CAACoE,IAAI,CACjCC,GAAmB,IAAKA,GAAG,CAAC/B,EAAE,IAAIiB,SAAS,CAC7C;QACD,IAAInC,cAAc,EAAE,IAAI,CAACkD,iBAAiB,CAAClD,cAAc,CAAC;;KAE7D,MAAM;MACL,IAAI,CAACkC,cAAc,CAACC,SAAS,CAAC,CAACjD,SAAS,CAAEa,OAAO,IAAI;QACnD,IAAI,CAACmD,iBAAiB,CAACnD,OAAO,CAAC;MACjC,CAAC,CAAC;;EAEN;EAEcmD,iBAAiBA,CAACC,cAA8B;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MAC5D,IAAIC,MAAM,CAACC,IAAI,CAACJ,cAAc,CAAC,CAACvD,MAAM,GAAG,CAAC,EAAE;QAC1C,MAAM4D,WAAW,SAASJ,KAAI,CAACK,oBAAoB,CAACN,cAAc,CAAC;QACnEC,KAAI,CAACxF,YAAY,CAAC8F,uBAAuB,CAACF,WAAW,CAAC;QACtDJ,KAAI,CAACxF,YAAY,CAAC+F,0BAA0B,CAC1CP,KAAI,CAACrD,OAAO,EACZyD,WAAW,CAACI,aAAa,EACzBJ,WAAW,CAACK,aAAa,CAC1B;QACDT,KAAI,CAACrD,OAAO,CAAC+D,UAAU,CAACV,KAAI,CAACrD,OAAO,CAACgE,cAAc,CAAC;;IACrD;EACH;EAEMN,oBAAoBA,CAAC1D,OAAuB;IAAA,IAAAiE,MAAA;IAAA,OAAAX,iBAAA;MAChD,MAAMY,kBAAkB,SAASD,MAAI,CAAClG,gBAAgB,CAACoG,eAAe,CACpEnE,OAAO,CAACoE,OAAO,EACfpE,OAAO,CAACqE,aAAa,EACrBrE,OAAO,CAACmB,EAAG,CACZ;MAED,MAAMmD,mBAAmB,GACvBL,MAAI,CAACjG,eAAe,CAACuG,4BAA4B,CAC/CvE,OAAO,CAACwE,YAAY,EACpBP,MAAI,CAACtF,iBAAiB,CACvB;MACH,MAAM8F,sBAAsB,GAAGR,MAAI,CAAC/F,kBAAkB,CAACwG,iBAAiB,CACtE1E,OAAO,CAAC2E,QAAQ,EAChBV,MAAI,CAACtF,iBAAiB,CACvB;MAED,OAAO;QACLkF,aAAa,EAAE,CACb,GAAGK,kBAAkB,CAACL,aAAa,EACnC,GAAGK,kBAAkB,CAACU,aAAa,EACnC,GAAGN,mBAAmB,EACtB,GAAGG,sBAAsB,CAC1B;QACDX,aAAa,EAAEI,kBAAkB,CAACJ;OACnC;IAAC;EACJ;EAEAe,wBAAwBA,CAAA;IACtB,IAAI,CAAC/G,WAAW,CAACgH,kBAAkB,EAAE;EACvC;EAEA;;;;EAIAC,uBAAuBA,CAACC,mBAA4B,EAAEC,YAAqB;IACzE,IAAI;MACF;MACA,IAAID,mBAAmB,EAAE;QACvB,IAAI,IAAI,CAAChF,OAAO,CAACkF,KAAK,CAACrB,aAAa,CAAChE,MAAM,GAAG,CAAC,EAAE;UAC/C,IAAI,CAACG,OAAO,CAACmF,cAAc,CAACC,SAAS,EAAE;UACvC,IAAI,CAACpF,OAAO,CAACqF,aAAa,CAAC;YACzBC,UAAU,EAAE,OAAO;YACnBC,UAAU,EAAE,MAAM;YAClBC,QAAQ,EAAGC,IAAU,IAAI;cACvB,IAAI,CAACjH,gBAAgB,CAACkH,YAAY,CAChCD,IAAI,EACJ,IAAI,CAACxF,cAAc,CAAC0F,IAAI,EACxB,KAAK,CACN;YACH;WACD,CAAC;SACH,MAAM;UACL,IAAI,CAACpH,eAAe,CAACqH,IAAI,CAAC,+BAA+B,CAAC;;OAE7D,MAAM;QACL,IAAI,CAACnD,iBAAiB,CAAC,IAAI,CAAClD,OAAO,CAAC4B,EAAG,CAAC,CAAChC,SAAS,CAAEN,QAAQ,IAAI;UAC9D,IAAI,CAACgH,mBAAmB,CAAC,IAAI,CAACtG,OAAO,CAAC4B,EAAG,EAAEtC,QAAQ,EAAEoG,YAAY,CAAC;QACpE,CAAC,CAAC;;KAEL,CAAC,OAAOa,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;;EAEhE;EAEcD,mBAAmBA,CAC/BnD,SAAiB,EACjB7D,QAA0B,EAC1BiC,cAAuB;IAAA,IAAAkF,MAAA;IAAA,OAAA1C,iBAAA;MAEvB,MAAM2C,SAAS,GAAGD,MAAI,CAACvH,eAAe,CAACyH,mBAAmB,EAAE;MAC5D,IAAI,CAACD,SAAS,IAAIA,SAAS,CAACpG,MAAM,KAAK,CAAC,EAAE;QACxCmG,MAAI,CAACzH,eAAe,CAACqH,IAAI,CAAC,+BAA+B,CAAC;QAC1D;;MAGF,MAAMO,YAAY,GAAG,OACbC,OAAO,CAACC,GAAG,CACfJ,SAAS,CACNK,MAAM,CAAEC,IAAI,IAAKA,IAAI,CAACnF,QAAQ,KAAK/D,gBAAgB,CAACmJ,OAAO,CAAC,CAC5DC,GAAG;QAAA,IAAAC,IAAA,GAAApD,iBAAA,CAAC,WAAOiD,IAAI,EAAI;UAClB,MAAMvG,OAAO,GAAGnB,QAAQ,CAACoE,IAAI,CAAEC,GAAG,IAAKA,GAAG,CAAC/B,EAAE,KAAKoF,IAAI,CAACzD,IAAI,EAAE3B,EAAE,CAAC;UAChE,IACEnB,OAAO,KACNA,OAAO,CAACoE,OAAO,CAACvE,MAAM,GAAG,CAAC,IACzBG,OAAO,CAACwE,YAAY,CAAC3E,MAAM,GAAG,CAAC,IAC/BG,OAAO,CAAC2E,QAAQ,CAAC9E,MAAM,GAAG,CAAC,CAAC,EAC9B;YACA,MAAM8G,SAAS,SAASX,MAAI,CAACY,wBAAwB,CAAC5G,OAAO,CAAC;YAC9D,OAAO;cACLoC,SAAS,EAAEpC,OAAO,CAACmB,EAAG;cACtB0F,KAAK,EAAEF,SAAS;cAChBhB,IAAI,EAAE3F,OAAO,CAAC2F;aACf;;UAEH,OAAO,IAAI;QACb,CAAC;QAAA,iBAAAmB,EAAA;UAAA,OAAAJ,IAAA,CAAAK,KAAA,OAAAC,SAAA;QAAA;MAAA,IAAC,CACL,EACDV,MAAM,CAAEb,IAAI,IAA4BA,IAAI,KAAK,IAAI,CAAC;MAExD,IAAIU,YAAY,CAACtG,MAAM,GAAG,CAAC,EAAE;QAC3BmG,MAAI,CAACxH,gBAAgB,CAACyI,WAAW,CAC/BvE,SAAS,EACTsD,MAAI,CAACzG,OAAO,CAACoG,IAAI,EACjB;UACEjD,SAAS;UACT7D,QAAQ,EAAEsH;SACX,EACDrF,cAAc,CACf;QACD,MAAMd,OAAO,GAAGnB,QAAQ,CAACoE,IAAI,CAC1BjD,OAAO,IAAKA,OAAO,CAACmB,EAAE,IAAI6E,MAAI,CAAC/F,cAAc,CAACkB,EAAE,CAClD;QACD,IAAInB,OAAO,EAAE;UACX,MAAMyD,WAAW,SAASuC,MAAI,CAACtC,oBAAoB,CAAC1D,OAAO,CAAC;UAC5DgG,MAAI,CAACnI,YAAY,CAAC+F,0BAA0B,CAC1CoC,MAAI,CAAChG,OAAO,EACZyD,WAAW,CAACI,aAAa,EACzBJ,WAAW,CAACK,aAAa,CAC1B;;OAEJ,MAAM;QACLkC,MAAI,CAACzH,eAAe,CAACqH,IAAI,CAAC,+BAA+B,CAAC;;IAC3D;EACH;EAEcgB,wBAAwBA,CACpC5G,OAAuB;IAAA,IAAAkH,MAAA;IAAA,OAAA5D,iBAAA;MAEvB,MAAMG,WAAW,SAASyD,MAAI,CAACxD,oBAAoB,CAAC1D,OAAO,CAAC;MAE5D,OAAO,IAAIoG,OAAO,CAAEe,OAAO,IAAI;QAC7B,MAAMC,GAAG,GAAGC,QAAQ,CAACC,cAAc,CAAC,YAAY,CAAC;QACjD,IAAI,CAACF,GAAG,EAAE;QAEV,MAAMG,WAAW,GAAGpK,EAAE,CAACqJ,OAAO,CAACgB,OAAO,CAACJ,GAAG,CAAC;QAC3C,IAAI,CAACG,WAAW,EAAE;QAElBL,MAAI,CAACrJ,YAAY,CAAC+F,0BAA0B,CAC1C2D,WAAW,EACX9D,WAAW,CAACI,aAAa,EACzBJ,WAAW,CAACK,aAAa,CAC1B;QACDyD,WAAW,CAACpC,cAAc,CAACC,SAAS,EAAE;QAEtCmC,WAAW,CAAClC,aAAa,CAAC;UACxBC,UAAU,EAAE,OAAO;UACnBE,QAAQ,EAAGC,IAAI,IAAI;YACjB,IAAIA,IAAI,EAAE;cACR0B,OAAO,CAAC1B,IAAI,CAAC;;UAEjB,CAAC;UACDF,UAAU,EAAE;SACb,CAAC;MACJ,CAAC,CAAC;IAAC;EACL;EAAC,QAAAkC,CAAA,G;qBA9ZUlK,cAAc,EAAAmK,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,iBAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,aAAA,GAAAL,EAAA,CAAAC,QAAA,CAAAK,EAAA,CAAAC,cAAA,GAAAP,EAAA,CAAAC,QAAA,CAAAO,EAAA,CAAAC,eAAA,GAAAT,EAAA,CAAAC,QAAA,CAAAS,EAAA,CAAAC,YAAA,GAAAX,EAAA,CAAAC,QAAA,CAAAW,EAAA,CAAAC,WAAA,GAAAb,EAAA,CAAAC,QAAA,CAAAa,EAAA,CAAAC,gBAAA,GAAAf,EAAA,CAAAC,QAAA,CAAAe,EAAA,CAAAC,sBAAA,GAAAjB,EAAA,CAAAC,QAAA,CAAAiB,EAAA,CAAAC,iBAAA,GAAAnB,EAAA,CAAAC,QAAA,CAAAmB,GAAA,CAAAC,kBAAA,GAAArB,EAAA,CAAAC,QAAA,CAAAqB,GAAA,CAAAC,iBAAA,GAAAvB,EAAA,CAAAC,QAAA,CAAAuB,GAAA,CAAAC,oBAAA,GAAAzB,EAAA,CAAAC,QAAA,CAAAyB,GAAA,CAAAC,kBAAA,GAAA3B,EAAA,CAAAC,QAAA,CAAA2B,GAAA,CAAAC,sBAAA,GAAA7B,EAAA,CAAAC,QAAA,CAAA6B,GAAA,CAAAC,eAAA,GAAA/B,EAAA,CAAAC,QAAA,CAAA+B,GAAA,CAAAC,gBAAA,GAAAjC,EAAA,CAAAC,QAAA,CAAAiC,GAAA,CAAAC,eAAA,GAAAnC,EAAA,CAAAC,QAAA,CAAAmC,GAAA,CAAAC,qBAAA;EAAA;EAAA,QAAAC,EAAA,G;WAAdzM,cAAc;IAAA0M,OAAA,EAAd1M,cAAc,CAAA2M,IAAA;IAAAC,UAAA,EAFb;EAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}