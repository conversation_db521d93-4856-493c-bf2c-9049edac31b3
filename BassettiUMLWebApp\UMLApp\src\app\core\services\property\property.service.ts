import { Injectable, signal } from '@angular/core';
import * as go from 'gojs';
import { BehaviorSubject, Observable } from 'rxjs';

import {
  GojsDiagramAttributeNode,
  GojsDiagramClassNode,
  GojsDiagramEnumerationNode,
  GojsDiagramLiteralNode,
  GojsFolderNode,
  GojsLinkNode,
  GojsNodeCategory,
} from 'src/app/shared/model/gojs';

@Injectable({
  providedIn: 'root',
})
export class PropertyService {
  // Convert to signals
  private _propertyData = signal<
    | GojsDiagramClassNode
    | GojsDiagramEnumerationNode
    | GojsDiagramAttributeNode
    | GojsDiagramLiteralNode
    | GojsFolderNode
    | GojsLinkNode
    | null
  >(null);

  public readonly propertyData = this._propertyData.asReadonly();

  // Keep BehaviorSubject for backward compatibility during transition
  private _propertyDataSubject = new BehaviorSubject<
    | GojsDiagramClassNode
    | GojsDiagramEnumerationNode
    | GojsDiagramAttributeNode
    | GojsDiagramLiteralNode
    | GojsFolderNode
    | GojsLinkNode
    | null
  >(null);

  constructor() {}

  setPropertyData(
    propertyData:
      | GojsDiagramClassNode
      | GojsDiagramEnumerationNode
      | GojsDiagramAttributeNode
      | GojsDiagramLiteralNode
      | GojsFolderNode
      | GojsLinkNode
      | null
  ): void {
    this._propertyData.set(propertyData);
    this._propertyDataSubject.next(propertyData);
  }

  propertyDataChanges(): Observable<
    | GojsDiagramClassNode
    | GojsDiagramEnumerationNode
    | GojsDiagramAttributeNode
    | GojsDiagramLiteralNode
    | GojsFolderNode
    | GojsLinkNode
    | null
  > {
    return this._propertyDataSubject.asObservable();
  }

  transferDataOnSelection(node: go.Part) {
    const nodeData = node.data;
    if (node.isSelected) {
      if (
        nodeData.category == GojsNodeCategory.Class ||
        nodeData.category == GojsNodeCategory.AssociativeClass
      ) {
        this.setPropertyData({
          id: nodeData.id,
          key: nodeData.key,
          color: nodeData.color,
          icon: nodeData.icon,
          name: nodeData.name,
          category: nodeData.category,
          size: nodeData.size,
          isGroup: nodeData.isGroup,
          supportingLevels: nodeData.supportingLevels,
          allowTopLevelDrops: nodeData.allowTopLevelDrops,
          editable: nodeData.editable,
          idTemplateClass: nodeData.idTemplateClass,
          items: nodeData.items,
          position: nodeData.position,
          showTablePanel: nodeData.showTablePanel,
          description: nodeData.description,
          tag: nodeData.tag,
          volumetry: nodeData.volumetry,
          treeNodeTag: nodeData.treeNodeTag,
        } as GojsDiagramClassNode);
      } else if (nodeData.category == GojsNodeCategory.Enumeration) {
        this.setPropertyData({
          id: nodeData.id,
          key: nodeData.key,
          color: nodeData.color,
          icon: nodeData.icon,
          name: nodeData.name,
          category: nodeData.category,
          size: nodeData.size,
          isGroup: nodeData.isGroup,
          supportingLevels: nodeData.supportingLevels,
          allowTopLevelDrops: nodeData.allowTopLevelDrops,
          editable: nodeData.editable,
          idTemplateEnumeration: nodeData.idTemplateEnumeration,
          items: nodeData.items,
          position: nodeData.position,
          showTablePanel: nodeData.showTablePanel,
          description: nodeData.description,
          tag: nodeData.tag,
          volumetry: nodeData.volumetry,
          treeNodeTag: nodeData.treeNodeTag,
        } as GojsDiagramEnumerationNode);
      } else if (
        nodeData.category == GojsNodeCategory.Attribute ||
        nodeData.category == GojsNodeCategory.Operation
      ) {
        this.setPropertyData({
          id: nodeData.id,
          key: nodeData.key,
          icon: nodeData.icon,
          name: nodeData.name,
          description: nodeData?.description,
          category: nodeData.category,
          isGroup: nodeData.isGroup,
          allowTopLevelDrops: nodeData.allowTopLevelDrops,
          editable: nodeData.editable,
          showTablePanel: nodeData.showTablePanel,
          dataType: nodeData.dataType,
          isAttribute: nodeData.isAttribute,
          memberType: nodeData.memberType,
          idClass: nodeData.idClass,
          treeNodeTag: nodeData.treeNodeTag,
        } as GojsDiagramAttributeNode);
      } else if (nodeData.category == GojsNodeCategory.EnumerationLiteral) {
        this.setPropertyData({
          id: nodeData.id,
          key: nodeData.key,
          icon: nodeData.icon,
          name: nodeData.name,
          category: nodeData.category,
          isGroup: nodeData.isGroup,
          allowTopLevelDrops: nodeData.allowTopLevelDrops,
          editable: nodeData.editable,
          showTablePanel: nodeData.showTablePanel,
          dataType: nodeData.dataType,
          isAttribute: nodeData.isAttribute,
          idEnumeration: nodeData.idEnumeration,
          treeNodeTag: nodeData.treeNodeTag,
        } as GojsDiagramLiteralNode);
      } else if (nodeData.category == GojsNodeCategory.Folder) {
        this.setPropertyData({
          id: nodeData.id,
          key: nodeData.key,
          icon: nodeData.icon,
          name: nodeData.name,
          category: nodeData.category,
          isGroup: nodeData.isGroup,
          allowTopLevelDrops: nodeData.allowTopLevelDrops,
          editable: nodeData.editable,
          showTablePanel: nodeData.showTablePanel,
          parent: nodeData.parent,
          idFolder: nodeData.idFolder,
          isFolder: nodeData.isFolder,
        } as GojsFolderNode);
      } else if ((nodeData.category = GojsNodeCategory.Association)) {
        this.setPropertyData({
          cardinalityFrom: nodeData.cardinalityFrom,
          cardinalityTo: nodeData.cardinalityTo,
          category: nodeData.category,
          color: nodeData.color,
          editable: nodeData.editable,
          from: nodeData.from,
          fromPort: nodeData.fromPort,
          id: nodeData.id,
          idDestinationTempClass: nodeData.idDestinationTempClass,
          idFromClass: nodeData.idFromClass,
          idLinkType: nodeData.idLinkType,
          idSourceTempClass: nodeData.idSourceTempClass,
          idToClass: nodeData.idToClass,
          key: nodeData.key,
          name: nodeData.name,
          to: nodeData.to,
          toPort: nodeData.toPort,
          fromComment: nodeData.fromComment,
          toComment: nodeData.toComment,
          segmentOffset: nodeData.segmentOffset,
        } as GojsLinkNode);
      }
    } else {
      this.setPropertyData(null);
    }
  }
}
